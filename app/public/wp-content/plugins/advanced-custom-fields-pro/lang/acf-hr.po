# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2023-05-04T13:57:24+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: hr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:17
msgid "%s fields"
msgstr ""

#: includes/admin/post-types/admin-taxonomies.php:260
msgid "No terms"
msgstr ""

#: includes/admin/post-types/admin-taxonomies.php:233
msgid "No post types"
msgstr ""

#: includes/admin/post-types/admin-post-types.php:256
msgid "No posts"
msgstr ""

#: includes/admin/post-types/admin-post-types.php:230
msgid "No taxonomies"
msgstr ""

#: includes/admin/post-types/admin-post-types.php:175
#: includes/admin/post-types/admin-taxonomies.php:175
msgid "No field groups"
msgstr ""

#: includes/admin/post-types/admin-field-groups.php:259
msgid "No fields"
msgstr ""

#: includes/admin/post-types/admin-field-groups.php:132
#: includes/admin/post-types/admin-post-types.php:139
#: includes/admin/post-types/admin-taxonomies.php:139
msgid "No description"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:484
#: includes/fields/class-acf-field-post_object.php:396
#: includes/fields/class-acf-field-relationship.php:608
msgid "Any post status"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:278
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:273
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:246
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:241
msgid "The taxonomy key must be under 20 characters."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr ""

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found in Trash"
msgstr ""

#: includes/post-types/class-acf-post-type.php:98
msgid "No Post Types found"
msgstr ""

#: includes/post-types/class-acf-post-type.php:97
msgid "Search Post Types"
msgstr ""

#: includes/post-types/class-acf-post-type.php:96
msgid "View Post Type"
msgstr ""

#: includes/post-types/class-acf-post-type.php:95
msgid "New Post Type"
msgstr ""

#: includes/post-types/class-acf-post-type.php:94
msgid "Edit Post Type"
msgstr ""

#: includes/post-types/class-acf-post-type.php:93
msgid "Add New Post Type"
msgstr ""

#: includes/post-types/class-acf-post-type.php:338
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:312
#: includes/post-types/class-acf-taxonomy.php:252
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""

#: includes/post-types/class-acf-post-type.php:306
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""

#: includes/post-types/class-acf-post-type.php:301
msgid "The post type key must be under 20 characters."
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:27
msgid "We do not recommend using this field in ACF Blocks."
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:27
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "WYSIWYG Editor"
msgstr ""

#: includes/fields/class-acf-field-user.php:22
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""

#: includes/fields/class-acf-field-url.php:26
msgid "A text input specifically designed for storing web addresses."
msgstr ""

#: includes/fields/class-acf-field-url.php:25
msgid "URL"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:27
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""

#: includes/fields/class-acf-field-time_picker.php:27
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""

#: includes/fields/class-acf-field-textarea.php:26
msgid "A basic textarea input for storing paragraphs of text."
msgstr ""

#: includes/fields/class-acf-field-text.php:26
msgid "A basic text input, useful for storing single string values."
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:30
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""

#: includes/fields/class-acf-field-tab.php:28
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""

#: includes/fields/class-acf-field-select.php:27
msgid "A dropdown list with a selection of choices that you specify."
msgstr ""

#: includes/fields/class-acf-field-relationship.php:27
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""

#: includes/fields/class-acf-field-range.php:26
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""

#: includes/fields/class-acf-field-radio.php:27
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""

#: includes/fields/class-acf-field-post_object.php:27
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""

#: includes/fields/class-acf-field-password.php:26
msgid "An input for providing a password using a masked field."
msgstr ""

#: includes/fields/class-acf-field-page_link.php:476
#: includes/fields/class-acf-field-post_object.php:388
#: includes/fields/class-acf-field-relationship.php:600
msgid "Filter by Post Status"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:27
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""

#: includes/fields/class-acf-field-oembed.php:27
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""

#: includes/fields/class-acf-field-number.php:26
msgid "An input limited to numerical values."
msgstr ""

#: includes/fields/class-acf-field-message.php:28
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""

#: includes/fields/class-acf-field-link.php:27
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""

#: includes/fields/class-acf-field-image.php:27
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr ""

#: includes/fields/class-acf-field-group.php:27
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""

#: includes/fields/class-acf-field-google-map.php:27
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""

#: includes/fields/class-acf-field-file.php:27
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr ""

#: includes/fields/class-acf-field-email.php:26
msgid "A text input specifically designed for storing email addresses."
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:27
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:27
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:27
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:27
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""

#: includes/fields/class-acf-field-button-group.php:26
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""

#: includes/fields/class-acf-field-accordion.php:27
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""

#: includes/fields.php:473
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""

#: includes/fields.php:463
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""

#: includes/fields.php:453
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""

#: includes/fields.php:444
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Kloniraj"

#: includes/fields.php:357
msgid "PRO"
msgstr ""

#: includes/fields.php:355
msgid "Advanced"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:85
msgid "JSON (newer)"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Original"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:55
msgid "Invalid post ID."
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid post type selected for review."
msgstr ""

#: includes/admin/views/global/navigation.php:104
msgid "More"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:86
msgid "Tutorial"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:75
msgid "Available with ACF PRO"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:63
msgid "Select Field"
msgstr ""

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:50
msgid "Try a different search term or browse %s"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:47
msgid "Popular fields"
msgstr ""

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:40
msgid "No search results for '%s'"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:13
msgid "Search fields..."
msgstr ""

#: includes/admin/views/browse-fields-modal.php:11
msgid "Select Field Type"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr ""

#: includes/admin/views/acf-taxonomy/list-empty.php:7
msgid "Add Taxonomy"
msgstr ""

#: includes/admin/views/acf-taxonomy/list-empty.php:6
msgid "Create custom taxonomies to classify post type content"
msgstr ""

#: includes/admin/views/acf-taxonomy/list-empty.php:5
msgid "Add Your First Taxonomy"
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:75
msgid "One or many post types that can be classified with this taxonomy."
msgstr ""

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:44
msgid "genre"
msgstr ""

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:26
msgid "Genre"
msgstr ""

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:9
msgid "Genres"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1129
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1073
msgid "Expose this post type in the REST API."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1051
msgid "Customize the query variable name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1024
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:977
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:937
msgid "Customize the slug used in the URL"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:920
msgid "Permalinks for this taxonomy are disabled."
msgstr ""

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:917
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:909
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1026
#: includes/admin/views/acf-taxonomy/basic-settings.php:41
msgid "Taxonomy Key"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:907
msgid "Select the type of permalink to use for this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:892
msgid "Display a column for the taxonomy on post type listing screens."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:891
msgid "Show Admin Column"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:878
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:877
msgid "Quick Edit"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:864
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:863
msgid "Tag Cloud"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:820
msgid ""
"A PHP function name to be called tor sanitizing taxonomy data saved from a "
"meta box."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:819
msgid "Meta Box Sanitization Callback"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:801
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:800
msgid "Register Meta Box Callback"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:759
msgid "No Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:758
msgid "Custom Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:754
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:753
msgid "Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:742
#: includes/admin/views/acf-taxonomy/advanced-settings.php:763
msgid "Categories Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:741
#: includes/admin/views/acf-taxonomy/advanced-settings.php:762
msgid "Tags Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:700
msgid "A link to a tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:699
msgid "Describes a navigation link block variation used in the block editor."
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:694
msgid "A link to a %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:679
msgid "Tag Link"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:659
msgid "← Go to tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:658
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "Back To Items"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:653
msgid "← Go to %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:638
msgid "Tags list"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "Assigns text to the table hidden heading."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:618
msgid "Tags list navigation"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "Assigns text to the table pagination hidden heading."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:593
msgid "Filter by category"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:592
msgid "Assigns text to the filter button in the posts lists table."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter By Item"
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:587
msgid "Filter by %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:571
#: includes/admin/views/acf-taxonomy/advanced-settings.php:572
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:570
msgid "Describes the Description field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:569
msgid "Description Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:550
#: includes/admin/views/acf-taxonomy/advanced-settings.php:551
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:549
msgid "Describes the Parent field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:548
msgid "Parent Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:534
#: includes/admin/views/acf-taxonomy/advanced-settings.php:535
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:533
msgid "Describes the Slug field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:532
msgid "Slug Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:518
#: includes/admin/views/acf-taxonomy/advanced-settings.php:519
msgid "The name is how it appears on your site"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:517
msgid "Describes the Name field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:516
msgid "Name Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:503
msgid "No tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:502
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No Terms"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:497
msgid "No %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:482
msgid "No tags found"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:481
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "Not Found"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:459
msgid "Assigns text to the Title field of the Most Used tab."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:458
#: includes/admin/views/acf-taxonomy/advanced-settings.php:460
#: includes/admin/views/acf-taxonomy/advanced-settings.php:461
msgid "Most Used"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:440
msgid "Choose from the most used tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:439
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose From Most Used"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:434
msgid "Choose from the most used %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:414
msgid "Add or remove tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:413
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add Or Remove Items"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:408
msgid "Add or remove %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:388
msgid "Separate tags with commas"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:387
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate Items With Commas"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:382
msgid "Separate %s with commas"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:362
msgid "Popular Tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:360
msgid "Popular Items"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:357
msgid "Popular %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:343
msgid "Search Tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Assigns search items text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:319
msgid "Parent Category:"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:317
msgid "Parent Item With Colon"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:294
msgid "Parent Category"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:292
msgid "Parent Item"
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:289
msgid "Parent %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:274
msgid "New Tag Name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "Assigns the new item name text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:272
msgid "New Item Name"
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:269
msgid "New %s Name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:254
msgid "Add New Tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Assigns the add new item text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:234
msgid "Update Tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Assigns the update item text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:232
msgid "Update Item"
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:229
msgid "Update %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:214
msgid "View Tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "In the admin bar to view term during editing."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:194
msgid "Edit Tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "At the top of the editor screen when editing a term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:174
msgid "All Tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "Assigns the all items text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:154
msgid "Assigns the menu name text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:153
msgid "Menu Label"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:127
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:111
msgid "A descriptive summary of the taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:91
msgid "A descriptive summary of the term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:90
msgid "Term Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:72
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:71
msgid "Term Slug"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:52
msgid "The name of the default term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:51
msgid "Term Name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:37
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:36
msgid "Default Term"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:24
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:23
msgid "Sort Terms"
msgstr ""

#: includes/admin/views/acf-post-type/list-empty.php:7
msgid "Add Post Type"
msgstr ""

#: includes/admin/views/acf-post-type/list-empty.php:6
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""

#: includes/admin/views/acf-post-type/list-empty.php:5
msgid "Add Your First Post Type"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:120
#: includes/admin/views/acf-taxonomy/basic-settings.php:119
msgid "I know what I'm doing, show me all the options."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:119
#: includes/admin/views/acf-taxonomy/basic-settings.php:118
msgid "Advanced Configuration"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Hierarchical post types can have descendants (like pages)."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/advanced-settings.php:976
#: includes/admin/views/acf-taxonomy/basic-settings.php:105
msgid "Hierarchical"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:91
msgid "Visible on the frontend and in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:90
#: includes/admin/views/acf-taxonomy/basic-settings.php:90
msgid "Public"
msgstr ""

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:43
msgid "movie"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:41
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr ""

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:25
msgid "Movie"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:23
#: includes/admin/views/acf-taxonomy/basic-settings.php:24
msgid "Singular Label"
msgstr ""

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:8
msgid "Movies"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:6
#: includes/admin/views/acf-taxonomy/basic-settings.php:7
msgid "Plural Label"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1250
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1249
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1128
msgid "Controller Class"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1231
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1109
msgid "The namespace part of the REST API URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1230
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1108
msgid "Namespace Route"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1212
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1090
msgid "The base URL for the post type REST API URLs."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1211
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1089
msgid "Base URL"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1197
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1196
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1072
msgid "Show In REST API"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1175
msgid "Customize the query variable name."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1174
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1050
msgid "Query Variable"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1152
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1028
msgid "No Query Variable Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1151
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1027
msgid "Custom Query Variable"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1147
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1023
msgid "Query Variable Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:999
msgid "URLs for an item and items can be accessed with a query string."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:998
msgid "Publicly Queryable"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1100
msgid "Custom slug for the Archive URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1099
msgid "Archive Slug"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1086
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1085
msgid "Archive"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1065
msgid "Pagination support for the items URLs such as the archives."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1064
msgid "Pagination"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1047
msgid "RSS feed URL for the post type items."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1046
msgid "Feed URL"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1028
#: includes/admin/views/acf-taxonomy/advanced-settings.php:957
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1027
#: includes/admin/views/acf-taxonomy/advanced-settings.php:956
msgid "Front URL Prefix"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1008
msgid "Customize the slug used in the URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1007
#: includes/admin/views/acf-taxonomy/advanced-settings.php:936
msgid "URL Slug"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:991
msgid "Permalinks for this post type are disabled."
msgstr ""

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:990
#: includes/admin/views/acf-taxonomy/advanced-settings.php:919
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:982
#: includes/admin/views/acf-taxonomy/advanced-settings.php:911
msgid "No Permalink (prevent URL rewriting)"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:981
#: includes/admin/views/acf-taxonomy/advanced-settings.php:910
msgid "Custom Permalink"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:980
#: includes/admin/views/acf-post-type/advanced-settings.php:1150
#: includes/admin/views/acf-post-type/basic-settings.php:40
msgid "Post Type Key"
msgstr ""

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:978
#: includes/admin/views/acf-post-type/advanced-settings.php:988
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:976
#: includes/admin/views/acf-taxonomy/advanced-settings.php:906
msgid "Permalink Rewrite"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:962
msgid "Delete items by a user when that user is deleted."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:961
msgid "Delete With User"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:947
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:946
msgid "Can Export"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:915
msgid "Optionally provide a plural to be used in capabilities."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:914
msgid "Plural Capability Name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:896
msgid "Choose another post type to base the capabilities for this post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:895
msgid "Singular Capability Name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:881
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:880
msgid "Rename Capabilities"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:866
msgid "Sets whether posts should be excluded from search results."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:865
msgid "Exclude From Search"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:852
#: includes/admin/views/acf-taxonomy/advanced-settings.php:850
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:851
#: includes/admin/views/acf-taxonomy/advanced-settings.php:849
msgid "Appearance Menus Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:833
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:832
msgid "Show In Admin Bar"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:801
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:800
msgid "Custom Meta Box Callback"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:780
msgid "Menu Icon"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:762
msgid "The position in the sidebar menu in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:761
msgid "Menu Position"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:743
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:742
msgid "Admin Menu Parent"
msgstr ""

#. translators: %s = "dashicon class name", link to the WordPress dashicon
#. documentation.
#: includes/admin/views/acf-post-type/advanced-settings.php:730
msgid ""
"The icon used for the post type menu item in the admin dashboard. Can be a "
"URL or %s to use for the icon."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:725
msgid "Dashicon class name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:714
#: includes/admin/views/acf-taxonomy/advanced-settings.php:730
msgid "Admin editor navigation in the sidebar menu."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:713
#: includes/admin/views/acf-taxonomy/advanced-settings.php:729
msgid "Show In Admin Menu"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:700
#: includes/admin/views/acf-taxonomy/advanced-settings.php:715
msgid "Items can be edited and managed in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:699
#: includes/admin/views/acf-taxonomy/advanced-settings.php:714
msgid "Show In UI"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:685
msgid "A link to a post."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:684
msgid "Description for a navigation link block variation."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:683
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "Item Link Description"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:679
msgid "A link to a %s."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:664
msgid "Post Link"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:663
msgid "Title for a navigation link block variation."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:662
#: includes/admin/views/acf-taxonomy/advanced-settings.php:677
msgid "Item Link"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:659
#: includes/admin/views/acf-taxonomy/advanced-settings.php:674
msgid "%s Link"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:644
msgid "Post updated."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:643
msgid "In the editor notice after an item is updated."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:642
msgid "Item Updated"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:639
msgid "%s updated."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:624
msgid "Post scheduled."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:623
msgid "In the editor notice after scheduling an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:622
msgid "Item Scheduled"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:619
msgid "%s scheduled."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:604
msgid "Post reverted to draft."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:603
msgid "In the editor notice after reverting an item to draft."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:602
msgid "Item Reverted To Draft"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:599
msgid "%s reverted to draft."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:584
msgid "Post published privately."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:583
msgid "In the editor notice after publishing a private item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:582
msgid "Item Published Privately"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:579
msgid "%s published privately."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:564
msgid "Post published."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:563
msgid "In the editor notice after publishing an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:562
msgid "Item Published"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:559
msgid "%s published."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:544
msgid "Posts list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:543
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:542
#: includes/admin/views/acf-taxonomy/advanced-settings.php:636
msgid "Items List"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:539
#: includes/admin/views/acf-taxonomy/advanced-settings.php:633
msgid "%s list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:524
msgid "Posts list navigation"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:523
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:616
msgid "Items List Navigation"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:519
#: includes/admin/views/acf-taxonomy/advanced-settings.php:613
msgid "%s list navigation"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:503
msgid "Filter posts by date"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:502
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:501
msgid "Filter Items By Date"
msgstr ""

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:497
msgid "Filter %s by date"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:482
msgid "Filter posts list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:481
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:480
msgid "Filter Items List"
msgstr ""

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:476
msgid "Filter %s list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:460
msgid "In the media modal showing all media uploaded to this item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:459
msgid "Uploaded To This Item"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:455
msgid "Uploaded to this %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:440
msgid "Insert into post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:439
msgid "As the button label when adding media to content."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:438
msgid "Insert Into Media Button"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:434
msgid "Insert into %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:419
msgid "Use as featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:418
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:417
msgid "Use Featured Image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:404
msgid "Remove featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:403
msgid "As the button label when removing the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:402
msgid "Remove Featured Image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:389
msgid "Set featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:388
msgid "As the button label when setting the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:387
msgid "Set Featured Image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:374
msgid "Featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:373
msgid "In the editor used for the title of the featured image meta box."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:372
msgid "Featured Image Meta Box"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:359
msgid "Post Attributes"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:358
msgid "In the editor used for the title of the post attributes meta box."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:357
msgid "Attributes Meta Box"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:354
msgid "%s Attributes"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:339
msgid "Post Archives"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:338
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:337
msgid "Archives Nav Menu"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:334
msgid "%s Archives"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:319
msgid "No posts found in Trash"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:318
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:317
msgid "No Items Found in Trash"
msgstr ""

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:313
msgid "No %s found in Trash"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:298
msgid "No posts found"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:297
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:296
msgid "No Items Found"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:292
#: includes/admin/views/acf-taxonomy/advanced-settings.php:476
msgid "No %s found"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:277
msgid "Search Posts"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:276
msgid "At the top of the items screen when searching for an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:275
#: includes/admin/views/acf-taxonomy/advanced-settings.php:341
msgid "Search Items"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:272
#: includes/admin/views/acf-taxonomy/advanced-settings.php:338
msgid "Search %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:257
msgid "Parent Page:"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:256
msgid "For hierarchical types in the post type list screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:255
msgid "Parent Item Prefix"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:252
#: includes/admin/views/acf-taxonomy/advanced-settings.php:314
msgid "Parent %s:"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:237
msgid "New Post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:235
msgid "New Item"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:232
msgid "New %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:202
msgid "Add New Post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:201
msgid "At the top of the editor screen when adding a new item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:200
#: includes/admin/views/acf-taxonomy/advanced-settings.php:252
msgid "Add New Item"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:197
#: includes/admin/views/acf-taxonomy/advanced-settings.php:249
msgid "Add New %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:182
msgid "View Posts"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:181
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:180
msgid "View Items"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:162
msgid "View Post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:161
msgid "In the admin bar to view item when editing it."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:160
#: includes/admin/views/acf-taxonomy/advanced-settings.php:212
msgid "View Item"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:157
#: includes/admin/views/acf-post-type/advanced-settings.php:177
#: includes/admin/views/acf-taxonomy/advanced-settings.php:209
msgid "View %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:142
msgid "Edit Post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:141
msgid "At the top of the editor screen when editing an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:140
#: includes/admin/views/acf-taxonomy/advanced-settings.php:192
msgid "Edit Item"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:137
#: includes/admin/views/acf-taxonomy/advanced-settings.php:189
msgid "Edit %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:122
msgid "All Posts"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-post-type/advanced-settings.php:216
#: includes/admin/views/acf-post-type/advanced-settings.php:236
msgid "In the post type submenu in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:120
#: includes/admin/views/acf-taxonomy/advanced-settings.php:172
msgid "All Items"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:117
#: includes/admin/views/acf-taxonomy/advanced-settings.php:169
msgid "All %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:101
msgid "Admin menu name for the post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:100
msgid "Menu Name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:86
#: includes/admin/views/acf-taxonomy/advanced-settings.php:138
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:84
#: includes/admin/views/acf-taxonomy/advanced-settings.php:136
msgid "Regenerate"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:75
msgid "Active post types are enabled and registered with WordPress."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:59
msgid "A descriptive summary of the post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:44
msgid "Add Custom"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:38
msgid "Enable various features in the content editor."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Post Formats"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:21
msgid "Editor"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:20
msgid "Trackbacks"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:71
msgid "Select existing taxonomies to classify items of the post type."
msgstr ""

#: includes/admin/views/acf-field-group/field.php:141
msgid "Browse Fields"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:292
msgid "Nothing to import"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ""

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:278
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/tools/class-acf-admin-tool-import.php:262
msgid "Failed to import taxonomies."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:244
msgid "Failed to import post types."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:233
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:209
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/tools/class-acf-admin-tool-import.php:122
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:127
msgid "Import from Custom Post Type UI"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:390
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's functions."
"php file or include it within an external file, then deactivate or delete "
"the items from the ACF admin."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:389
msgid "Export - Generate PHP"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:362
msgid "Export"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:276
msgid "Select Taxonomies"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:254
msgid "Select Post Types"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:167
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/post-types/admin-taxonomy.php:124
#: assets/build/js/acf-internal-post-type.js:144
#: assets/build/js/acf-internal-post-type.js:204
msgid "Category"
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:122
#: assets/build/js/acf-internal-post-type.js:141
#: assets/build/js/acf-internal-post-type.js:201
msgid "Tag"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:103
msgid "Create new post type"
msgstr ""

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr ""

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr ""

#: includes/admin/post-types/admin-taxonomies.php:344
#: includes/admin/post-types/admin-taxonomy.php:152
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:305
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/post-types/admin-taxonomies.php:106
msgid "Terms"
msgstr ""

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:319
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:312
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:305
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:298
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/post-types/admin-post-types.php:79
#: includes/admin/post-types/admin-taxonomies.php:104
#: includes/admin/tools/class-acf-admin-tool-import.php:82
#: includes/admin/views/acf-taxonomy/basic-settings.php:66
#: includes/post-types/class-acf-post-type.php:90
msgid "Post Types"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:159
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Advanced Settings"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:158
msgid "Basic Settings"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:152
#: includes/admin/post-types/admin-post-types.php:337
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:125
#: assets/build/js/acf-internal-post-type.js:138
#: assets/build/js/acf-internal-post-type.js:198
msgid "Pages"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:102
msgid "Create new taxonomy"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:101
#: includes/admin/post-types/admin-taxonomy.php:101
msgid "Link existing field groups"
msgstr ""

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:82
msgid "%s post type created"
msgstr ""

#. translators: %s post type name
#. translators: %s taxonomy name
#: includes/admin/post-types/admin-post-type.php:78
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr ""

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:120
#: assets/build/js/acf-field-group.js:1146
#: assets/build/js/acf-field-group.js:1366
msgid "Type to search..."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:105
#: assets/build/js/acf-field-group.js:1172
#: assets/build/js/acf-field-group.js:2295
#: assets/build/js/acf-field-group.js:1414
#: assets/build/js/acf-field-group.js:2689
msgid "PRO Only"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:97
#: assets/build/js/acf-internal-post-type.js:270
#: assets/build/js/acf-internal-post-type.js:365
msgid "Field groups linked successfully."
msgstr ""

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:194
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""

#: includes/admin/admin.php:48
msgid "ACF"
msgstr ""

#: includes/admin/admin-internal-post-type.php:338
msgid "taxonomy"
msgstr ""

#: includes/admin/admin-internal-post-type.php:338
msgid "post type"
msgstr ""

#. translators: %1$s - name of newly created post. %2$s - either "post type" or
#. "taxonomy".
#: includes/admin/admin-internal-post-type.php:336
msgid "Link %1$s %2$s to field groups"
msgstr ""

#: includes/admin/admin-internal-post-type.php:329
msgid "Done"
msgstr ""

#: includes/admin/admin-internal-post-type.php:316
msgid "Field group(s)"
msgstr ""

#: includes/admin/admin-internal-post-type.php:315
msgid "Select one or many field groups..."
msgstr ""

#: includes/admin/admin-internal-post-type.php:314
msgid "Please select the field groups to link."
msgstr ""

#: includes/admin/admin-internal-post-type.php:278
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/admin-internal-post-type-list.php:255
#: includes/admin/post-types/admin-post-types.php:338
#: includes/admin/post-types/admin-taxonomies.php:345
msgctxt "post status"
msgid "Registration Failed"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:254
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""

#: includes/acf-internal-post-type-functions.php:482
#: includes/acf-internal-post-type-functions.php:510
msgid "REST API"
msgstr ""

#: includes/acf-internal-post-type-functions.php:481
msgid "Permissions"
msgstr ""

#: includes/acf-internal-post-type-functions.php:480
#: includes/acf-internal-post-type-functions.php:509
msgid "URLs"
msgstr ""

#: includes/acf-internal-post-type-functions.php:479
#: includes/acf-internal-post-type-functions.php:508
msgid "Visibility"
msgstr ""

#: includes/acf-internal-post-type-functions.php:478
#: includes/acf-internal-post-type-functions.php:507
msgid "Labels"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:243
msgid "Field Settings Tabs"
msgstr ""

#. Author URI of the plugin
msgid ""
"https://wpengine.com/?utm_source=wordpress."
"org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""

#: includes/api/api-template.php:867
msgid "[ACF shortcode value disabled for preview]"
msgstr ""

#: includes/admin/admin-internal-post-type.php:288
#: includes/admin/post-types/admin-field-group.php:545
msgid "Close Modal"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:96
#: assets/build/js/acf-field-group.js:1661
#: assets/build/js/acf-field-group.js:1980
msgid "Field moved to other group"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:95
#: assets/build/js/acf.js:1440 assets/build/js/acf.js:1521
msgid "Close modal"
msgstr ""

#: includes/fields/class-acf-field-tab.php:125
msgid "Start a new group of tabs at this tab."
msgstr ""

#: includes/fields/class-acf-field-tab.php:124
msgid "New Tab Group"
msgstr ""

#: includes/fields/class-acf-field-select.php:457
#: includes/fields/class-acf-field-true_false.php:200
msgid "Use a stylized checkbox using select2"
msgstr ""

#: includes/fields/class-acf-field-radio.php:260
msgid "Save Other Choice"
msgstr ""

#: includes/fields/class-acf-field-radio.php:249
msgid "Allow Other Choice"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:450
msgid "Add Toggle All"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:409
msgid "Save Custom Values"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:398
msgid "Allow Custom Values"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:148
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""

#: pro/admin/admin-updates.php:122,
#: pro/admin/views/html-settings-updates.php:12
msgid "Updates"
msgstr "Ažuriranja"

#: includes/admin/views/global/navigation.php:83
msgid "Advanced Custom Fields logo"
msgstr ""

#: includes/admin/views/global/form-top.php:57
msgid "Save Changes"
msgstr ""

#: includes/admin/views/global/form-top.php:44
msgid "Field Group Title"
msgstr ""

#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr ""

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:20
#: includes/admin/views/acf-post-type/list-empty.php:12
#: includes/admin/views/acf-taxonomy/list-empty.php:12
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""

#: includes/admin/views/acf-field-group/list-empty.php:15
msgid "Add Field Group"
msgstr ""

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:10
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""

#: includes/admin/views/acf-field-group/list-empty.php:5
msgid "Add Your First Field Group"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:16
msgid "Upgrade Now"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:11
msgid "Options Pages"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:10
msgid "ACF Blocks"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:8
msgid "Gallery Field"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:7
msgid "Flexible Content Field"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "Repeater Field"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:4
#: includes/admin/views/global/navigation.php:125
msgid "Unlock Extra Features with ACF PRO"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:252
msgid "Delete Field Group"
msgstr ""

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:246
msgid "Created on %1$s at %2$s"
msgstr ""

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr ""

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr ""

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:61
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""

#: includes/admin/views/acf-field-group/fields.php:54
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""

#: includes/admin/views/acf-field-group/fields.php:53
msgid "Add Your First Field"
msgstr ""

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:32
msgid "#"
msgstr ""

#: includes/admin/views/acf-field-group/fields.php:22
#: includes/admin/views/acf-field-group/fields.php:56
#: includes/admin/views/acf-field-group/fields.php:92
#: includes/admin/views/global/form-top.php:53
msgid "Add Field"
msgstr ""

#: includes/acf-field-group-functions.php:496 includes/fields.php:410
msgid "Presentation"
msgstr ""

#: includes/fields.php:409
msgid "Validation"
msgstr ""

#: includes/acf-internal-post-type-functions.php:477
#: includes/acf-internal-post-type-functions.php:506 includes/fields.php:408
msgid "General"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:70
msgid "Import JSON"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:370
msgid "Export As JSON"
msgstr ""

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:345
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:338
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/admin-internal-post-type-list.php:430
#: includes/admin/admin-internal-post-type-list.php:461
msgid "Deactivate"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:430
msgid "Deactivate this item"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:426
#: includes/admin/admin-internal-post-type-list.php:460
msgid "Activate"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Activate this item"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:92
#: assets/build/js/acf-field-group.js:2741
#: assets/build/js/acf-field-group.js:3180
msgid "Move field group to trash?"
msgstr ""

#: acf.php:485 includes/admin/admin-internal-post-type-list.php:242
#: includes/admin/post-types/admin-field-group.php:271
#: includes/admin/post-types/admin-post-type.php:292
#: includes/admin/post-types/admin-taxonomy.php:292
msgctxt "post status"
msgid "Inactive"
msgstr ""

#. Author of the plugin
msgid "WP Engine"
msgstr ""

#: acf.php:543
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""

#: acf.php:541
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""

#: includes/acf-value-functions.php:374
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" "
"target=\"_blank\">Learn how to fix this</a>."
msgstr ""

#: includes/fields/class-acf-field-user.php:540
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/fields/class-acf-field-user.php:531
msgid "%1$s must have a valid user ID."
msgstr ""

#: includes/fields/class-acf-field-user.php:369
msgid "Invalid request."
msgstr ""

#: includes/fields/class-acf-field-select.php:690
msgid "%1$s is not one of %2$s"
msgstr ""

#: includes/fields/class-acf-field-post_object.php:698
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/fields/class-acf-field-post_object.php:682
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/fields/class-acf-field-post_object.php:673
msgid "%1$s must have a valid post ID."
msgstr ""

#: includes/fields/class-acf-field-file.php:475
msgid "%s requires a valid attachment ID."
msgstr ""

#: includes/admin/views/acf-field-group/options.php:218
msgid "Show in REST API"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:170
msgid "Enable Transparency"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:189
msgid "RGBA Array"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:99
msgid "RGBA String"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:98
#: includes/fields/class-acf-field-color_picker.php:188
msgid "Hex String"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:65
msgid "Upgrade to PRO"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:271
#: includes/admin/post-types/admin-post-type.php:292
#: includes/admin/post-types/admin-taxonomy.php:292
msgctxt "post status"
msgid "Active"
msgstr ""

#: includes/fields/class-acf-field-email.php:181
msgid "'%s' is not a valid email address"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:77
msgid "Color value"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:75
msgid "Select default color"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:73
msgid "Clear color"
msgstr ""

#: includes/acf-wp-functions.php:87
msgid "Blocks"
msgstr ""

#: includes/acf-wp-functions.php:83
msgid "Options"
msgstr "Postavke"

#: includes/acf-wp-functions.php:79
msgid "Users"
msgstr ""

#: includes/acf-wp-functions.php:75
msgid "Menu items"
msgstr ""

#: includes/acf-wp-functions.php:67
msgid "Widgets"
msgstr ""

#: includes/acf-wp-functions.php:59
msgid "Attachments"
msgstr ""

#: includes/acf-wp-functions.php:54
#: includes/admin/post-types/admin-post-types.php:104
#: includes/admin/post-types/admin-taxonomies.php:79
#: includes/admin/tools/class-acf-admin-tool-import.php:93
#: includes/admin/views/acf-post-type/basic-settings.php:70
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr ""

#: includes/acf-wp-functions.php:41
#: includes/admin/post-types/admin-post-type.php:123
#: includes/admin/post-types/admin-post-types.php:106
#: includes/admin/views/acf-post-type/advanced-settings.php:102
#: assets/build/js/acf-internal-post-type.js:135
#: assets/build/js/acf-internal-post-type.js:195
msgid "Posts"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:76
msgid "Last updated: %s"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:70
msgid "Sorry, this post is unavailable for diff comparison."
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:42
msgid "Invalid field group parameter(s)."
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:396
msgid "Awaiting save"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:393
msgid "Saved"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:389
#: includes/admin/tools/class-acf-admin-tool-import.php:49
msgid "Import"
msgstr "Uvoz"

#: includes/admin/admin-internal-post-type-list.php:385
msgid "Review changes"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:361
msgid "Located in: %s"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:358
msgid "Located in plugin: %s"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:355
msgid "Located in theme: %s"
msgstr ""

#: includes/admin/post-types/admin-field-groups.php:239
msgid "Various"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:210
#: includes/admin/admin-internal-post-type-list.php:468
msgid "Sync changes"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:209
msgid "Loading diff"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:208
msgid "Review local JSON changes"
msgstr ""

#: includes/admin/admin.php:169
msgid "Visit website"
msgstr ""

#: includes/admin/admin.php:168
msgid "View details"
msgstr ""

#: includes/admin/admin.php:167
msgid "Version %s"
msgstr ""

#: includes/admin/admin.php:166
msgid "Information"
msgstr ""

#: includes/admin/admin.php:157
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""

#: includes/admin/admin.php:153
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""

#: includes/admin/admin.php:149
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""

#: includes/admin/admin.php:146
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""

#: includes/admin/admin.php:143 includes/admin/admin.php:145
msgid "Help & Support"
msgstr ""

#: includes/admin/admin.php:134
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""

#: includes/admin/admin.php:131
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""

#: includes/admin/admin.php:129
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""

#: includes/admin/admin.php:126 includes/admin/admin.php:128
msgid "Overview"
msgstr ""

#: includes/locations.php:36
msgid "Location type \"%s\" is already registered."
msgstr ""

#: includes/locations.php:25
msgid "Class \"%s\" does not exist."
msgstr ""

#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr ""

#: includes/fields/class-acf-field-user.php:364
msgid "Error loading field."
msgstr ""

#: assets/build/js/acf-input.js:2750 assets/build/js/acf-input.js:2819
#: assets/build/js/acf-input.js:2926 assets/build/js/acf-input.js:3000
msgid "Location not found: %s"
msgstr ""

#: includes/forms/form-user.php:353
msgid "<strong>Error</strong>: %s"
msgstr ""

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Widget"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Tip korisnika"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Komentar"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Format objave"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Stavka izbornika"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Status objave"

#: includes/acf-wp-functions.php:71
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Izbornici"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Lokacije izbornika"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Izbornik"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Taksonomija objave"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Pod-stranica (Ima matičnu stranicu)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Matičan stranica (Ima podstranice)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Matična stranica (Nije podstranica)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Stranica za objave"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Početna stranica"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Tip stranice"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Prikazuje administracijki dio"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Prikazuje web stranicu"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Prijavljen"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Trenutni korisnik"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Predložak stranice"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Registriraj"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Dodaj / Uredi"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Korisnički obrazac"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Matična stranica"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Super Admin"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Trenutni tip korisnika"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Zadani predložak"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Predložak stranice"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Kategorija objave"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Svi oblici %s"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Prilog"

#: includes/validation.php:364
msgid "%s value is required"
msgstr "%s je obavezno"

#: includes/admin/views/acf-field-group/conditional-logic.php:59
msgid "Show this field if"
msgstr "Prikaži polje ako"

#: includes/admin/views/acf-field-group/conditional-logic.php:26
#: includes/admin/views/acf-field-group/field.php:105 includes/fields.php:411
msgid "Conditional Logic"
msgstr "Uvjet za prikaz"

#: includes/admin/admin.php:234
#: includes/admin/views/acf-field-group/conditional-logic.php:156
#: includes/admin/views/acf-field-group/location-rule.php:91
msgid "and"
msgstr "i"

#: includes/admin/post-types/admin-field-groups.php:101
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:110
msgid "Local JSON"
msgstr "Učitavanje polja iz JSON datoteke"

#: includes/admin/views/acf-field-group/pro-features.php:9
msgid "Clone Field"
msgstr ""

#: includes/admin/views/upgrade/notice.php:30
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""

#: includes/admin/views/upgrade/notice.php:28
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""

#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr ""

#: includes/admin/views/upgrade/notice.php:27
msgid "Database Upgrade Required"
msgstr "Potrebno je nadograditi bazu podataka"

#: includes/admin/post-types/admin-field-group.php:132
#: includes/admin/views/upgrade/notice.php:18
msgid "Options Page"
msgstr "Postavke"

#: includes/admin/views/upgrade/notice.php:15 includes/fields.php:460
msgid "Gallery"
msgstr "Galerija"

#: includes/admin/views/upgrade/notice.php:12 includes/fields.php:450
msgid "Flexible Content"
msgstr "Fleksibilno polje"

#: includes/admin/views/upgrade/notice.php:9 includes/fields.php:470
msgid "Repeater"
msgstr "Ponavljajuće polje"

#: includes/admin/views/tools/tools.php:24
msgid "Back to all tools"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:180
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Ukoliko je više skupova polja prikazano na istom ekranu, postavke prvog "
"skupa polja će biti korištene (postavke polja sa nižim brojem u redosljedu)"

#: includes/admin/views/acf-field-group/options.php:180
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Odaberite</b> koje grupe želite <b>sakriti</b> prilikom uređivanja."

#: includes/admin/views/acf-field-group/options.php:179
msgid "Hide on screen"
msgstr "Sakrij"

#: includes/admin/views/acf-field-group/options.php:171
msgid "Send Trackbacks"
msgstr "Pošalji povratnu vezu"

#: includes/admin/post-types/admin-taxonomy.php:123
#: includes/admin/views/acf-field-group/options.php:170
#: includes/admin/views/acf-taxonomy/advanced-settings.php:155
#: assets/build/js/acf-internal-post-type.js:142
#: assets/build/js/acf-internal-post-type.js:202
msgid "Tags"
msgstr "Oznake"

#: includes/admin/post-types/admin-taxonomy.php:125
#: includes/admin/views/acf-field-group/options.php:169
#: assets/build/js/acf-internal-post-type.js:145
#: assets/build/js/acf-internal-post-type.js:205
msgid "Categories"
msgstr "Kategorije"

#: includes/admin/views/acf-field-group/options.php:167
#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Page Attributes"
msgstr "Atributi stranice"

#: includes/admin/views/acf-field-group/options.php:166
msgid "Format"
msgstr "Format"

#: includes/admin/views/acf-field-group/options.php:165
#: includes/admin/views/acf-post-type/advanced-settings.php:18
msgid "Author"
msgstr "Autor"

#: includes/admin/views/acf-field-group/options.php:164
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/acf-field-group/options.php:163
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Revisions"
msgstr "Revizija"

#: includes/acf-wp-functions.php:63
#: includes/admin/views/acf-field-group/options.php:162
#: includes/admin/views/acf-post-type/advanced-settings.php:19
msgid "Comments"
msgstr "Komentari"

#: includes/admin/views/acf-field-group/options.php:161
msgid "Discussion"
msgstr "Rasprava"

#: includes/admin/views/acf-field-group/options.php:159
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Excerpt"
msgstr "Izvadak"

#: includes/admin/views/acf-field-group/options.php:158
msgid "Content Editor"
msgstr "Uređivač sadržaja"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Permalink"
msgstr "Stalna veza"

#: includes/admin/views/acf-field-group/options.php:235
msgid "Shown in field group list"
msgstr "Vidljivo u popisu"

#: includes/admin/views/acf-field-group/options.php:142
msgid "Field groups with a lower order will appear first"
msgstr "Skup polja sa nižim brojem će biti više pozicioniran"

#: includes/admin/views/acf-field-group/options.php:141
msgid "Order No."
msgstr "Redni broj."

#: includes/admin/views/acf-field-group/options.php:132
msgid "Below fields"
msgstr "Iznad oznake"

#: includes/admin/views/acf-field-group/options.php:131
msgid "Below labels"
msgstr "Ispod oznake"

#: includes/admin/views/acf-field-group/options.php:124
msgid "Instruction placement"
msgstr "Pozicija uputa"

#: includes/admin/views/acf-field-group/options.php:107
msgid "Label placement"
msgstr "Pozicija oznake"

#: includes/admin/views/acf-field-group/options.php:97
msgid "Side"
msgstr "Desni stupac"

#: includes/admin/views/acf-field-group/options.php:96
msgid "Normal (after content)"
msgstr "Normalno (nakon saržaja)"

#: includes/admin/views/acf-field-group/options.php:95
msgid "High (after title)"
msgstr "Visoko (nakon naslova)"

#: includes/admin/views/acf-field-group/options.php:88
msgid "Position"
msgstr "Pozicija"

#: includes/admin/views/acf-field-group/options.php:79
msgid "Seamless (no metabox)"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:78
msgid "Standard (WP metabox)"
msgstr "Zadano (WP metabox)"

#: includes/admin/views/acf-field-group/options.php:71
msgid "Style"
msgstr "Stil"

#: includes/admin/views/acf-field-group/fields.php:44
msgid "Type"
msgstr "Tip"

#: includes/admin/post-types/admin-field-groups.php:95
#: includes/admin/post-types/admin-post-types.php:103
#: includes/admin/post-types/admin-taxonomies.php:103
#: includes/admin/views/acf-field-group/fields.php:43
msgid "Key"
msgstr "Ključ"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:37
msgid "Order"
msgstr "Redni broj"

#: includes/admin/views/acf-field-group/field.php:318
msgid "Close Field"
msgstr "Zatvori polje"

#: includes/admin/views/acf-field-group/field.php:249
msgid "id"
msgstr "id"

#: includes/admin/views/acf-field-group/field.php:233
msgid "class"
msgstr "klasa"

#: includes/admin/views/acf-field-group/field.php:275
msgid "width"
msgstr "širina"

#: includes/admin/views/acf-field-group/field.php:269
msgid "Wrapper Attributes"
msgstr "Značajke prethodnog elementa"

#: includes/admin/views/acf-field-group/field.php:192
msgid "Required"
msgstr "Obavezno?"

#: includes/admin/views/acf-field-group/field.php:217
msgid "Instructions for authors. Shown when submitting data"
msgstr "Upute priliko uređivanja. Vidljivo prilikom spremanja podataka"

#: includes/admin/views/acf-field-group/field.php:216
msgid "Instructions"
msgstr "Upute"

#: includes/admin/views/acf-field-group/field.php:125
msgid "Field Type"
msgstr "Tip polja"

#: includes/admin/views/acf-field-group/field.php:166
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Jedna riječ, bez razmaka. Povlaka i donja crta su dozvoljeni"

#: includes/admin/views/acf-field-group/field.php:165
msgid "Field Name"
msgstr "Naziv polja"

#: includes/admin/views/acf-field-group/field.php:153
msgid "This is the name which will appear on the EDIT page"
msgstr "Naziv koji se prikazuje prilikom uređivanja stranice"

#: includes/admin/views/acf-field-group/field.php:152
#: includes/admin/views/browse-fields-modal.php:59
msgid "Field Label"
msgstr "Naziv polja"

#: includes/admin/views/acf-field-group/field.php:77
msgid "Delete"
msgstr "Obriši"

#: includes/admin/views/acf-field-group/field.php:77
msgid "Delete field"
msgstr "Obriši polje"

#: includes/admin/views/acf-field-group/field.php:75
msgid "Move"
msgstr "Premjesti"

#: includes/admin/views/acf-field-group/field.php:75
msgid "Move field to another group"
msgstr "Premjeti polje u drugu skupinu"

#: includes/admin/views/acf-field-group/field.php:73
msgid "Duplicate field"
msgstr "Dupliciraj polje"

#: includes/admin/views/acf-field-group/field.php:69
#: includes/admin/views/acf-field-group/field.php:72
msgid "Edit field"
msgstr "Uredi polje"

#: includes/admin/views/acf-field-group/field.php:65
msgid "Drag to reorder"
msgstr "Presloži polja povlačenjem"

#: includes/admin/post-types/admin-field-group.php:103
#: includes/admin/views/acf-field-group/location-group.php:3
#: assets/build/js/acf-field-group.js:2323
#: assets/build/js/acf-field-group.js:2725
msgid "Show this field group if"
msgstr "Prikaži ovaj skup polja ako"

#: includes/admin/views/upgrade/upgrade.php:94
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "Nema novih nadogradnji."

#: includes/admin/views/upgrade/upgrade.php:33
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""

#: includes/admin/views/upgrade/upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "Učitavam podatke za nadogradnju…"

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:65
msgid "Upgrade failed."
msgstr ""

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr ""

#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:31
msgid "Upgrading data to version %s"
msgstr "Nadogradnja na verziju %s"

#: includes/admin/views/upgrade/network.php:121
#: includes/admin/views/upgrade/notice.php:44
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Prije nego nastavite preporučamo da napravite sigurnosnu kopiju baze "
"podataka. Jeste li sigurni da želite nastaviti ažuriranje?"

#: includes/admin/views/upgrade/network.php:117
msgid "Please select at least one site to upgrade."
msgstr ""

#: includes/admin/views/upgrade/network.php:97
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Baza podataka je nadograđena. <a href=\"%s\">Kliknite ovdje za povratak na "
"administraciju WordPress mreže</a>"

#: includes/admin/views/upgrade/network.php:80
msgid "Site is up to date"
msgstr "Nema novih ažuriranja za web stranica"

#: includes/admin/views/upgrade/network.php:78
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr ""

#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Site"
msgstr "Web stranica"

#: includes/admin/views/upgrade/network.php:26
#: includes/admin/views/upgrade/network.php:27
#: includes/admin/views/upgrade/network.php:96
msgid "Upgrade Sites"
msgstr "Ažuriraj stranice"

#: includes/admin/views/upgrade/network.php:26
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Ažuriranje baze podatak dovršeno. Provjerite koje web stranice u svojoj "
"mreži želite nadograditi i zatim kliknite %s."

#: includes/admin/views/acf-field-group/conditional-logic.php:171
#: includes/admin/views/acf-field-group/locations.php:38
msgid "Add rule group"
msgstr "Dodaj skup pravila"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr "Odaberite pravila koja određuju koji prikaz će koristiti ACF polja"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "Pravila"

#: includes/admin/tools/class-acf-admin-tool-export.php:482
msgid "Copied"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:458
msgid "Copy to clipboard"
msgstr "Kopiraj u međuspremnik"

#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:233
msgid "Select Field Groups"
msgstr "Odaberite skup polja"

#: includes/admin/tools/class-acf-admin-tool-export.php:96
#: includes/admin/tools/class-acf-admin-tool-export.php:131
msgid "No field groups selected"
msgstr "Niste odabrali polje"

#: includes/admin/tools/class-acf-admin-tool-export.php:39
#: includes/admin/tools/class-acf-admin-tool-export.php:371
#: includes/admin/tools/class-acf-admin-tool-export.php:399
msgid "Generate PHP"
msgstr "Generiraj PHP kod"

#: includes/admin/tools/class-acf-admin-tool-export.php:35
msgid "Export Field Groups"
msgstr "Izvezi skup polja"

#: includes/admin/tools/class-acf-admin-tool-import.php:177
msgid "Import file empty"
msgstr "Odabrana datoteka za uvoz ne sadrži"

#: includes/admin/tools/class-acf-admin-tool-import.php:168
msgid "Incorrect file type"
msgstr "Nedozvoljeni format datoteke"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Error uploading file. Please try again"
msgstr "Greška prilikom prijenosa datoteke, molimo pokušaj ponovno"

#: includes/admin/tools/class-acf-admin-tool-import.php:50
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:27
msgid "Import Field Groups"
msgstr "Uvoz skupa polja"

#: includes/admin/admin-internal-post-type-list.php:384
msgid "Sync"
msgstr "Sinkroniziraj"

#: includes/admin/admin-internal-post-type-list.php:841
msgid "Select %s"
msgstr "Odaberi %s"

#: includes/admin/admin-internal-post-type-list.php:419
#: includes/admin/admin-internal-post-type-list.php:457
#: includes/admin/views/acf-field-group/field.php:73
msgid "Duplicate"
msgstr "Dupliciraj"

#: includes/admin/admin-internal-post-type-list.php:419
msgid "Duplicate this item"
msgstr "Dupliciraj"

#: includes/admin/views/acf-post-type/advanced-settings.php:37
msgid "Supports"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:92
msgid "Documentation"
msgstr ""

#: includes/admin/post-types/admin-field-groups.php:94
#: includes/admin/post-types/admin-post-types.php:102
#: includes/admin/post-types/admin-taxonomies.php:102
#: includes/admin/views/acf-field-group/options.php:234
#: includes/admin/views/acf-post-type/advanced-settings.php:58
#: includes/admin/views/acf-taxonomy/advanced-settings.php:110
#: includes/admin/views/upgrade/network.php:38
#: includes/admin/views/upgrade/network.php:49
msgid "Description"
msgstr "Opis"

#: includes/admin/admin-internal-post-type-list.php:381
#: includes/admin/admin-internal-post-type-list.php:730
msgid "Sync available"
msgstr "Sinkronizacija dostupna"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:359
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:352
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/admin-internal-post-type-list.php:131
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Aktivno <span class=\"count\">(%s)</span>"
msgstr[1] "Aktivno <span class=\"count\">(%s)</span>"
msgstr[2] "Aktivno <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:254
msgid "Review sites & upgrade"
msgstr "Pregledaj stranice i nadogradi"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:93
#: includes/admin/admin-upgrade.php:94 includes/admin/admin-upgrade.php:230
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/upgrade.php:26
msgid "Upgrade Database"
msgstr "Nadogradi bazu podataka"

#: includes/admin/views/acf-field-group/options.php:160
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Custom Fields"
msgstr "Dodatna polja"

#: includes/admin/post-types/admin-field-group.php:590
msgid "Move Field"
msgstr "Premjesti polje"

#: includes/admin/post-types/admin-field-group.php:579
#: includes/admin/post-types/admin-field-group.php:583
msgid "Please select the destination for this field"
msgstr "Odaberite lokaciju za ovo polje"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:541
msgid "The %1$s field can now be found in the %2$s field group"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:538
msgid "Move Complete."
msgstr "Premještanje dovršeno."

#: includes/admin/views/acf-field-group/field.php:35
#: includes/admin/views/acf-field-group/options.php:202
#: includes/admin/views/acf-post-type/advanced-settings.php:74
#: includes/admin/views/acf-taxonomy/advanced-settings.php:126
msgid "Active"
msgstr "Aktivan"

#: includes/admin/post-types/admin-field-group.php:240
msgid "Field Keys"
msgstr "Oznaka polja"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/admin/tools/class-acf-admin-tool-export.php:320
msgid "Settings"
msgstr "Postavke"

#: includes/admin/post-types/admin-field-groups.php:96
msgid "Location"
msgstr "Lokacija"

#: includes/admin/post-types/admin-field-group.php:104
#: assets/build/js/acf-input.js:983 assets/build/js/acf-input.js:1075
msgid "Null"
msgstr "Null"

#: includes/admin/post-types/admin-field-group.php:101
#: includes/class-acf-internal-post-type.php:729
#: includes/post-types/class-acf-field-group.php:345
#: assets/build/js/acf-field-group.js:1501
#: assets/build/js/acf-field-group.js:1808
msgid "copy"
msgstr "kopiraj"

#: includes/admin/post-types/admin-field-group.php:100
#: assets/build/js/acf-field-group.js:623
#: assets/build/js/acf-field-group.js:778
msgid "(this field)"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:98
#: assets/build/js/acf-input.js:918 assets/build/js/acf-input.js:943
#: assets/build/js/acf-input.js:1002 assets/build/js/acf-input.js:1030
msgid "Checked"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:94
#: assets/build/js/acf-field-group.js:1606
#: assets/build/js/acf-field-group.js:1920
msgid "Move Custom Field"
msgstr "Premjesti polje"

#: includes/admin/post-types/admin-field-group.php:93
#: assets/build/js/acf-field-group.js:649
#: assets/build/js/acf-field-group.js:804
msgid "No toggle fields available"
msgstr "Nema polja koji omoguću korisniku odabir"

#: includes/admin/post-types/admin-field-group.php:91
msgid "Field group title is required"
msgstr "Naziv polja je obavezna"

#: includes/admin/post-types/admin-field-group.php:90
#: assets/build/js/acf-field-group.js:1595
#: assets/build/js/acf-field-group.js:1906
msgid "This field cannot be moved until its changes have been saved"
msgstr "Potrebno je spremiti izmjene prije nego možete premjestiti polje"

#: includes/admin/post-types/admin-field-group.php:89
#: assets/build/js/acf-field-group.js:1405
#: assets/build/js/acf-field-group.js:1703
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Polje ne može započinjati sa “field_”, odabrite drugi naziv"

#: includes/admin/post-types/admin-field-group.php:71
msgid "Field group draft updated."
msgstr "Skica ažurirana."

#: includes/admin/post-types/admin-field-group.php:70
msgid "Field group scheduled for."
msgstr "Skup polja je označen za."

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group submitted."
msgstr "Skup polja je spremljen."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group saved."
msgstr "Skup polja spremljen."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group published."
msgstr "Skup polja objavljen."

#: includes/admin/post-types/admin-field-group.php:64
msgid "Field group deleted."
msgstr "Skup polja izbrisan."

#: includes/admin/post-types/admin-field-group.php:62
#: includes/admin/post-types/admin-field-group.php:63
#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group updated."
msgstr "Skup polja ažuriran."

#: includes/admin/admin-tools.php:118
#: includes/admin/views/global/navigation.php:138
#: includes/admin/views/tools/tools.php:21
msgid "Tools"
msgstr "Alati"

#: includes/locations/abstract-acf-location.php:106
msgid "is not equal to"
msgstr "je drukčije"

#: includes/locations/abstract-acf-location.php:105
msgid "is equal to"
msgstr "je jednako"

#: includes/locations.php:102
msgid "Forms"
msgstr "Forme"

#: includes/admin/post-types/admin-post-type.php:124 includes/locations.php:100
#: includes/locations/class-acf-location-page.php:22
#: assets/build/js/acf-internal-post-type.js:137
#: assets/build/js/acf-internal-post-type.js:197
msgid "Page"
msgstr "Stranice"

#: includes/admin/post-types/admin-post-type.php:122 includes/locations.php:99
#: includes/locations/class-acf-location-post.php:22
#: assets/build/js/acf-internal-post-type.js:134
#: assets/build/js/acf-internal-post-type.js:194
msgid "Post"
msgstr "Objava"

#: includes/fields.php:354
msgid "Relational"
msgstr "Relacijski"

#: includes/fields.php:353
msgid "Choice"
msgstr "Odabir"

#: includes/fields.php:351
msgid "Basic"
msgstr "Osnovno"

#: includes/fields.php:320
msgid "Unknown"
msgstr "Nepoznato polje"

#: includes/fields.php:320
msgid "Field type does not exist"
msgstr "Tip polja ne postoji"

#: includes/forms/form-front.php:236
msgid "Spam Detected"
msgstr "Spam"

#: includes/forms/form-front.php:107
msgid "Post updated"
msgstr "Objava ažurirana"

#: includes/forms/form-front.php:106
msgid "Update"
msgstr "Ažuriraj"

#: includes/forms/form-front.php:57
msgid "Validate Email"
msgstr "Verificiraj email"

#: includes/fields.php:352 includes/forms/form-front.php:49
msgid "Content"
msgstr "Sadržaj"

#: includes/admin/views/acf-post-type/advanced-settings.php:17
#: includes/forms/form-front.php:40
msgid "Title"
msgstr "Naziv"

#: includes/assets.php:372 includes/forms/form-comment.php:160
#: assets/build/js/acf-input.js:7358 assets/build/js/acf-input.js:7948
msgid "Edit field group"
msgstr "Uredi skup polja"

#: includes/admin/post-types/admin-field-group.php:117
#: assets/build/js/acf-input.js:1125 assets/build/js/acf-input.js:1230
msgid "Selection is less than"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:116
#: assets/build/js/acf-input.js:1106 assets/build/js/acf-input.js:1202
msgid "Selection is greater than"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:115
#: assets/build/js/acf-input.js:1075 assets/build/js/acf-input.js:1170
msgid "Value is less than"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:114
#: assets/build/js/acf-input.js:1045 assets/build/js/acf-input.js:1139
msgid "Value is greater than"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:113
#: assets/build/js/acf-input.js:888 assets/build/js/acf-input.js:960
msgid "Value contains"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:112
#: assets/build/js/acf-input.js:862 assets/build/js/acf-input.js:926
msgid "Value matches pattern"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:111
#: assets/build/js/acf-input.js:840 assets/build/js/acf-input.js:1023
#: assets/build/js/acf-input.js:903 assets/build/js/acf-input.js:1116
msgid "Value is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:110
#: assets/build/js/acf-input.js:810 assets/build/js/acf-input.js:964
#: assets/build/js/acf-input.js:864 assets/build/js/acf-input.js:1053
msgid "Value is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:109
#: assets/build/js/acf-input.js:788 assets/build/js/acf-input.js:841
msgid "Has no value"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:108
#: assets/build/js/acf-input.js:758 assets/build/js/acf-input.js:783
msgid "Has any value"
msgstr ""

#: includes/admin/admin-internal-post-type.php:328
#: includes/admin/views/browse-fields-modal.php:62 includes/assets.php:353
#: assets/build/js/acf.js:1567 assets/build/js/acf.js:1662
msgid "Cancel"
msgstr "Otkaži"

#: includes/assets.php:349 assets/build/js/acf.js:1741
#: assets/build/js/acf.js:1859
msgid "Are you sure?"
msgstr "Jeste li sigurni?"

#: includes/assets.php:369 assets/build/js/acf-input.js:9419
#: assets/build/js/acf-input.js:10274
msgid "%d fields require attention"
msgstr "Nekoliko polja treba vašu pažnje: %d"

#: includes/assets.php:368 assets/build/js/acf-input.js:9417
#: assets/build/js/acf-input.js:10270
msgid "1 field requires attention"
msgstr "1 polje treba vašu pažnju"

#: includes/assets.php:367 includes/validation.php:286
#: includes/validation.php:296 assets/build/js/acf-input.js:9412
#: assets/build/js/acf-input.js:10265
msgid "Validation failed"
msgstr "Verifikacija nije uspjela"

#: includes/assets.php:366 assets/build/js/acf-input.js:9575
#: assets/build/js/acf-input.js:10448
msgid "Validation successful"
msgstr "Uspješna verifikacija"

#: includes/media.php:54 assets/build/js/acf-input.js:7186
#: assets/build/js/acf-input.js:7752
msgid "Restricted"
msgstr "Ograničen pristup"

#: includes/media.php:53 assets/build/js/acf-input.js:7001
#: assets/build/js/acf-input.js:7516
msgid "Collapse Details"
msgstr "Sakrij detalje"

#: includes/media.php:52 assets/build/js/acf-input.js:7001
#: assets/build/js/acf-input.js:7513
msgid "Expand Details"
msgstr "Prošireni prikaz"

#: includes/admin/views/acf-post-type/advanced-settings.php:461
#: includes/media.php:51 assets/build/js/acf-input.js:6868
#: assets/build/js/acf-input.js:7361
msgid "Uploaded to this post"
msgstr "Postavljeno uz ovu objavu"

#: includes/media.php:50 assets/build/js/acf-input.js:6907
#: assets/build/js/acf-input.js:7400
msgctxt "verb"
msgid "Update"
msgstr "Ažuriraj"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Uredi"

#: includes/assets.php:363 assets/build/js/acf-input.js:9189
#: assets/build/js/acf-input.js:10036
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"Izmjene koje ste napravili bit će izgubljene ukoliko napustite ovu stranicu"

#: includes/api/api-helpers.php:3482
msgid "File type must be %s."
msgstr "Tip datoteke mora biti %s."

#: includes/admin/post-types/admin-field-group.php:102
#: includes/admin/views/acf-field-group/conditional-logic.php:59
#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:36
#: includes/api/api-helpers.php:3478 assets/build/js/acf-field-group.js:771
#: assets/build/js/acf-field-group.js:2361
#: assets/build/js/acf-field-group.js:933
#: assets/build/js/acf-field-group.js:2769
msgid "or"
msgstr "ili"

#: includes/api/api-helpers.php:3451
msgid "File size must not exceed %s."
msgstr ""

#: includes/api/api-helpers.php:3446
msgid "File size must be at least %s."
msgstr "Veličina datoteke mora biti najmanje %s."

#: includes/api/api-helpers.php:3431
msgid "Image height must not exceed %dpx."
msgstr "Visina slike ne smije biti veća od %dpx."

#: includes/api/api-helpers.php:3426
msgid "Image height must be at least %dpx."
msgstr "Visina slike mora biti najmanje %dpx."

#: includes/api/api-helpers.php:3412
msgid "Image width must not exceed %dpx."
msgstr "Širina slike ne smije biti veća od %dpx."

#: includes/api/api-helpers.php:3407
msgid "Image width must be at least %dpx."
msgstr "Širina slike mora biti najmanje %dpx."

#: includes/api/api-helpers.php:1653 includes/api/api-term.php:147
msgid "(no title)"
msgstr "(bez naziva)"

#: includes/api/api-helpers.php:944
msgid "Full Size"
msgstr "Puna veličina"

#: includes/api/api-helpers.php:903
msgid "Large"
msgstr "Velika"

#: includes/api/api-helpers.php:902
msgid "Medium"
msgstr "Srednja"

#: includes/api/api-helpers.php:901
msgid "Thumbnail"
msgstr "Sličica"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:99
#: assets/build/js/acf-field-group.js:1077
#: assets/build/js/acf-field-group.js:1260
msgid "(no label)"
msgstr ""

#: includes/fields/class-acf-field-textarea.php:145
msgid "Sets the textarea height"
msgstr "Podesi visinu tekstualnog polja"

#: includes/fields/class-acf-field-textarea.php:144
msgid "Rows"
msgstr "Broj redova"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Tekst polje"

#: includes/fields/class-acf-field-checkbox.php:451
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Dodaj okvir za izbor koji omogućje odabir svih opcija"

#: includes/fields/class-acf-field-checkbox.php:413
msgid "Save 'custom' values to the field's choices"
msgstr "Spremi ‘dodatne’ vrijednosti i prikaži ih omogući njihov odabir"

#: includes/fields/class-acf-field-checkbox.php:402
msgid "Allow 'custom' values to be added"
msgstr "Omogući ‘dodatne’ vrijednosti"

#: includes/fields/class-acf-field-checkbox.php:38
msgid "Add new choice"
msgstr "Dodaj odabir"

#: includes/fields/class-acf-field-checkbox.php:174
msgid "Toggle All"
msgstr "Sakrij sve"

#: includes/fields/class-acf-field-page_link.php:506
msgid "Allow Archives URLs"
msgstr "Omogući odabir arhive tipova"

#: includes/fields/class-acf-field-page_link.php:179
msgid "Archives"
msgstr "Arhiva"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "URL stranice"

#: includes/fields/class-acf-field-taxonomy.php:948
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Dodaj"

#: includes/admin/views/acf-field-group/fields.php:42
#: includes/fields/class-acf-field-taxonomy.php:913
msgid "Name"
msgstr "Naziv"

#: includes/fields/class-acf-field-taxonomy.php:897
msgid "%s added"
msgstr "Dodano: %s"

#: includes/fields/class-acf-field-taxonomy.php:861
msgid "%s already exists"
msgstr "%s već postoji"

#: includes/fields/class-acf-field-taxonomy.php:849
msgid "User unable to add new %s"
msgstr "Korisnik nije u mogućnosti dodati %s"

#: includes/fields/class-acf-field-taxonomy.php:759
msgid "Term ID"
msgstr "Vrijednost kao: ID pojma"

#: includes/fields/class-acf-field-taxonomy.php:758
msgid "Term Object"
msgstr "Vrijednost pojma kao objekt"

#: includes/fields/class-acf-field-taxonomy.php:743
msgid "Load value from posts terms"
msgstr "Učitaj pojmove iz objave"

#: includes/fields/class-acf-field-taxonomy.php:742
msgid "Load Terms"
msgstr "Učitaj pojmove"

#: includes/fields/class-acf-field-taxonomy.php:732
msgid "Connect selected terms to the post"
msgstr "Spoji odabrane pojmove sa objavom"

#: includes/fields/class-acf-field-taxonomy.php:731
msgid "Save Terms"
msgstr "Spremi pojmove"

#: includes/fields/class-acf-field-taxonomy.php:721
msgid "Allow new terms to be created whilst editing"
msgstr "Omogući kreiranje pojmova prilikom uređivanja"

#: includes/fields/class-acf-field-taxonomy.php:720
msgid "Create Terms"
msgstr "Kreiraj pojmove"

#: includes/fields/class-acf-field-taxonomy.php:779
msgid "Radio Buttons"
msgstr "Radiogumbi"

#: includes/fields/class-acf-field-taxonomy.php:778
msgid "Single Value"
msgstr "Jedan odabir"

#: includes/fields/class-acf-field-taxonomy.php:776
msgid "Multi Select"
msgstr "Više odabira"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:775
msgid "Checkbox"
msgstr "Skup dugmadi"

#: includes/fields/class-acf-field-taxonomy.php:774
msgid "Multiple Values"
msgstr "Omogući odabir više vrijednosti"

#: includes/fields/class-acf-field-taxonomy.php:769
msgid "Select the appearance of this field"
msgstr "Odaberite izgled polja"

#: includes/fields/class-acf-field-taxonomy.php:768
msgid "Appearance"
msgstr "Prikaz"

#: includes/fields/class-acf-field-taxonomy.php:710
msgid "Select the taxonomy to be displayed"
msgstr "Odaberite taksonomiju za prikaz"

#: includes/fields/class-acf-field-taxonomy.php:671
msgctxt "No Terms"
msgid "No %s"
msgstr "Nema %s"

#: includes/fields/class-acf-field-number.php:266
msgid "Value must be equal to or lower than %d"
msgstr "Unešena vrijednost mora biti jednaka ili niža od %d"

#: includes/fields/class-acf-field-number.php:259
msgid "Value must be equal to or higher than %d"
msgstr "Unešena vrijednost mora biti jednaka ili viša od %d"

#: includes/fields/class-acf-field-number.php:244
msgid "Value must be a number"
msgstr "Vrijednost mora biti broj"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Broj"

#: includes/fields/class-acf-field-radio.php:264
msgid "Save 'other' values to the field's choices"
msgstr "Spremi ostale vrijednosti i omogući njihov odabir"

#: includes/fields/class-acf-field-radio.php:253
msgid "Add 'other' choice to allow for custom values"
msgstr "Dodaj odabir ’ostalo’ za slobodan unost"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Radiogumb"

#: includes/fields/class-acf-field-accordion.php:107
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Preciziraj prijelomnu točku za prethoda polja accordion. Ovo će omogućiti "
"novi skup polja nakon prijelomne točke."

#: includes/fields/class-acf-field-accordion.php:96
msgid "Allow this accordion to open without closing others."
msgstr "Omogući prikaz ovog accordion polja bez zatvaranje ostalih."

#: includes/fields/class-acf-field-accordion.php:95
msgid "Multi-expand"
msgstr "Mulit-proširenje"

#: includes/fields/class-acf-field-accordion.php:85
msgid "Display this accordion as open on page load."
msgstr "Prikaži accordion polje kao otvoreno prilikom učitavanja."

#: includes/fields/class-acf-field-accordion.php:84
msgid "Open"
msgstr "Otvori"

#: includes/fields/class-acf-field-accordion.php:25
msgid "Accordion"
msgstr "Multi prošireno"

#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-file.php:279
msgid "Restrict which files can be uploaded"
msgstr "Ograniči tip datoteka koji se smije uvesti"

#: includes/fields/class-acf-field-file.php:220
msgid "File ID"
msgstr "Vrijednost kao ID"

#: includes/fields/class-acf-field-file.php:219
msgid "File URL"
msgstr "Putanja datoteke"

#: includes/fields/class-acf-field-file.php:218
msgid "File Array"
msgstr "Vrijednost kao niz"

#: includes/fields/class-acf-field-file.php:186
msgid "Add File"
msgstr "Dodaj datoteku"

#: includes/admin/tools/class-acf-admin-tool-import.php:156
#: includes/fields/class-acf-field-file.php:186
msgid "No file selected"
msgstr "Niste odabrali datoteku"

#: includes/fields/class-acf-field-file.php:150
msgid "File name"
msgstr "Naziv datoteke"

#: includes/fields/class-acf-field-file.php:63
#: assets/build/js/acf-input.js:2474 assets/build/js/acf-input.js:2625
msgid "Update File"
msgstr "Ažuriraj datoteku"

#: includes/fields/class-acf-field-file.php:62
#: assets/build/js/acf-input.js:2473 assets/build/js/acf-input.js:2624
msgid "Edit File"
msgstr "Uredi datoteku"

#: includes/admin/tools/class-acf-admin-tool-import.php:58
#: includes/fields/class-acf-field-file.php:61
#: assets/build/js/acf-input.js:2447 assets/build/js/acf-input.js:2597
msgid "Select File"
msgstr "Odaberite datoteku"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Datoteka"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Lozinka"

#: includes/fields/class-acf-field-select.php:398
msgid "Specify the value returned"
msgstr "Preciziraj vrijednost za povrat"

#: includes/fields/class-acf-field-select.php:467
msgid "Use AJAX to lazy load choices?"
msgstr "Asinkrono učitaj dostupne odabire?"

#: includes/fields/class-acf-field-checkbox.php:362
#: includes/fields/class-acf-field-select.php:387
msgid "Enter each default value on a new line"
msgstr "Unesite svaku novu vrijednost u zasebnu liniju"

#: includes/fields/class-acf-field-select.php:258 includes/media.php:48
#: assets/build/js/acf-input.js:6766 assets/build/js/acf-input.js:7246
msgctxt "verb"
msgid "Select"
msgstr "Odaberi"

#: includes/fields/class-acf-field-select.php:121
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Neuspješno učitavanje"

#: includes/fields/class-acf-field-select.php:120
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Pretražujem&hellip;"

#: includes/fields/class-acf-field-select.php:119
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Učitavam rezultate&hellip;"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Odabir opcija je ograničen na najviše %d"

#: includes/fields/class-acf-field-select.php:117
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Moguće je odabrati samo jednu opciju"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Molimo obrišite višak znakova - %d znak(ova) je višak"

#: includes/fields/class-acf-field-select.php:115
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Molimo obrišite 1 znak"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Molimo unesite najmanje %d ili više znakova"

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Molimo unesite 1 ili više znakova"

#: includes/fields/class-acf-field-select.php:112
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Nema rezultata"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d rezultata dostupno, za pomicanje koristite strelice gore/dole."

#: includes/fields/class-acf-field-select.php:110
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Jedan rezultat dostupan, pritisnite enter za odabir."

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:780
msgctxt "noun"
msgid "Select"
msgstr "Odaberi"

#: includes/fields/class-acf-field-user.php:77
msgid "User ID"
msgstr ""

#: includes/fields/class-acf-field-user.php:76
msgid "User Object"
msgstr ""

#: includes/fields/class-acf-field-user.php:75
msgid "User Array"
msgstr ""

#: includes/fields/class-acf-field-user.php:63
msgid "All user roles"
msgstr "Sve uloge"

#: includes/fields/class-acf-field-user.php:55
msgid "Filter by role"
msgstr "Filtar prema ulozi"

#: includes/fields/class-acf-field-user.php:20 includes/locations.php:101
msgid "User"
msgstr "Korisnik"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Razdjelnik"

#: includes/fields/class-acf-field-color_picker.php:76
msgid "Select Color"
msgstr "Odaberite boju"

#: includes/admin/post-types/admin-post-type.php:126
#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/fields/class-acf-field-color_picker.php:74
#: assets/build/js/acf-internal-post-type.js:54
#: assets/build/js/acf-internal-post-type.js:59
msgid "Default"
msgstr "Zadano"

#: includes/admin/views/acf-post-type/advanced-settings.php:85
#: includes/admin/views/acf-taxonomy/advanced-settings.php:137
#: includes/fields/class-acf-field-color_picker.php:72
msgid "Clear"
msgstr "Ukloni"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Odabir boje"

#: includes/fields/class-acf-field-date_time_picker.php:88
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:87
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:83
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Odaberi"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Završeno"

#: includes/fields/class-acf-field-date_time_picker.php:79
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Vremenska zona"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Mikrosekunda"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milisekunda"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Sekunda"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuta"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Sat"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Vrijeme"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Odaberi vrijeme"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Odabir datuma i sata"

#: includes/fields/class-acf-field-accordion.php:106
msgid "Endpoint"
msgstr "Prijelomna točka"

#: includes/admin/views/acf-field-group/options.php:115
#: includes/fields/class-acf-field-tab.php:115
msgid "Left aligned"
msgstr "Lijevo poravnato"

#: includes/admin/views/acf-field-group/options.php:114
#: includes/fields/class-acf-field-tab.php:114
msgid "Top aligned"
msgstr "Poravnato sa vrhom"

#: includes/fields/class-acf-field-tab.php:110
msgid "Placement"
msgstr "Pozicija"

#: includes/fields/class-acf-field-tab.php:26
msgid "Tab"
msgstr "Kartica"

#: includes/fields/class-acf-field-url.php:162
msgid "Value must be a valid URL"
msgstr "Vrijednost molja biti valjana"

#: includes/fields/class-acf-field-link.php:177
msgid "Link URL"
msgstr "Putanja poveznice"

#: includes/fields/class-acf-field-link.php:176
msgid "Link Array"
msgstr "Vrijednost kao niz"

#: includes/fields/class-acf-field-link.php:145
msgid "Opens in a new window/tab"
msgstr "Otvori u novom prozoru/kartici"

#: includes/fields/class-acf-field-link.php:140
msgid "Select Link"
msgstr "Odaberite poveznicu"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Poveznica"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "Email"

#: includes/fields/class-acf-field-number.php:188
#: includes/fields/class-acf-field-range.php:217
msgid "Step Size"
msgstr "Korak"

#: includes/fields/class-acf-field-number.php:158
#: includes/fields/class-acf-field-range.php:195
msgid "Maximum Value"
msgstr "Maksimum"

#: includes/fields/class-acf-field-number.php:148
#: includes/fields/class-acf-field-range.php:184
msgid "Minimum Value"
msgstr "Minimum"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Raspon"

#: includes/fields/class-acf-field-button-group.php:175
#: includes/fields/class-acf-field-checkbox.php:379
#: includes/fields/class-acf-field-radio.php:220
#: includes/fields/class-acf-field-select.php:405
msgid "Both (Array)"
msgstr "Oboje (podatkovni niz)"

#: includes/admin/views/acf-field-group/fields.php:41
#: includes/fields/class-acf-field-button-group.php:174
#: includes/fields/class-acf-field-checkbox.php:378
#: includes/fields/class-acf-field-radio.php:219
#: includes/fields/class-acf-field-select.php:404
msgid "Label"
msgstr "Oznaka"

#: includes/fields/class-acf-field-button-group.php:173
#: includes/fields/class-acf-field-checkbox.php:377
#: includes/fields/class-acf-field-radio.php:218
#: includes/fields/class-acf-field-select.php:403
msgid "Value"
msgstr "Vrijednost"

#: includes/fields/class-acf-field-button-group.php:222
#: includes/fields/class-acf-field-checkbox.php:441
#: includes/fields/class-acf-field-radio.php:292
msgid "Vertical"
msgstr "Vertikalno"

#: includes/fields/class-acf-field-button-group.php:221
#: includes/fields/class-acf-field-checkbox.php:442
#: includes/fields/class-acf-field-radio.php:293
msgid "Horizontal"
msgstr "Horizontalno"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-checkbox.php:352
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-select.php:376
msgid "red : Red"
msgstr "crvena : Crvena"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-checkbox.php:352
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-select.php:376
msgid "For more control, you may specify both a value and label like this:"
msgstr "Za bolju kontrolu unesite oboje, vrijednost i naziv, kao npr:"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-checkbox.php:352
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-select.php:376
msgid "Enter each choice on a new line."
msgstr "Svaki odabir je potrebno dodati kao novi red."

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:351
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-select.php:375
msgid "Choices"
msgstr "Mogući odabiri"

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Skup dugmadi"

#: includes/fields/class-acf-field-page_link.php:517
#: includes/fields/class-acf-field-post_object.php:433
#: includes/fields/class-acf-field-select.php:413
#: includes/fields/class-acf-field-user.php:86
msgid "Select multiple values?"
msgstr "Dozvoli odabir više vrijednosti?"

#: includes/fields/class-acf-field-button-group.php:194
#: includes/fields/class-acf-field-page_link.php:538
#: includes/fields/class-acf-field-post_object.php:455
#: includes/fields/class-acf-field-radio.php:238
#: includes/fields/class-acf-field-select.php:435
#: includes/fields/class-acf-field-taxonomy.php:789
#: includes/fields/class-acf-field-user.php:107
msgid "Allow Null?"
msgstr "Dozvoli null vrijednost?"

#: includes/fields/class-acf-field-page_link.php:263
#: includes/fields/class-acf-field-post_object.php:264
#: includes/fields/class-acf-field-taxonomy.php:935
msgid "Parent"
msgstr "Matični"

#: includes/fields/class-acf-field-wysiwyg.php:397
msgid "TinyMCE will not be initialized until field is clicked"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:396
msgid "Delay initialization?"
msgstr "Odgodi učitavanje?"

#: includes/fields/class-acf-field-wysiwyg.php:385
msgid "Show Media Upload Buttons?"
msgstr "Prikaži gumb za odabir datoteka?"

#: includes/fields/class-acf-field-wysiwyg.php:369
msgid "Toolbar"
msgstr "Alatna traka"

#: includes/fields/class-acf-field-wysiwyg.php:361
msgid "Text Only"
msgstr "Samo tekstualno"

#: includes/fields/class-acf-field-wysiwyg.php:360
msgid "Visual Only"
msgstr "Samo vizualni"

#: includes/fields/class-acf-field-wysiwyg.php:359
msgid "Visual & Text"
msgstr "Vizualno i tekstualno"

#: includes/fields/class-acf-field-wysiwyg.php:354
msgid "Tabs"
msgstr "Kartice"

#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Click to initialize TinyMCE"
msgstr "Aktiviraj vizualno uređivanje na klik"

#: includes/fields/class-acf-field-wysiwyg.php:286
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Tekst polje"

#: includes/fields/class-acf-field-wysiwyg.php:285
msgid "Visual"
msgstr "Vizualno"

#: includes/fields/class-acf-field-text.php:183
#: includes/fields/class-acf-field-textarea.php:236
msgid "Value must not exceed %d characters"
msgstr ""

#: includes/fields/class-acf-field-text.php:118
#: includes/fields/class-acf-field-textarea.php:124
msgid "Leave blank for no limit"
msgstr "Ostavite prazno za neograničeno"

#: includes/fields/class-acf-field-text.php:117
#: includes/fields/class-acf-field-textarea.php:123
msgid "Character Limit"
msgstr "Ograniči broj znakova"

#: includes/fields/class-acf-field-email.php:158
#: includes/fields/class-acf-field-number.php:209
#: includes/fields/class-acf-field-password.php:105
#: includes/fields/class-acf-field-range.php:239
#: includes/fields/class-acf-field-text.php:158
msgid "Appears after the input"
msgstr "Prikazuje se iza polja"

#: includes/fields/class-acf-field-email.php:157
#: includes/fields/class-acf-field-number.php:208
#: includes/fields/class-acf-field-password.php:104
#: includes/fields/class-acf-field-range.php:238
#: includes/fields/class-acf-field-text.php:157
msgid "Append"
msgstr "Umetni na kraj"

#: includes/fields/class-acf-field-email.php:148
#: includes/fields/class-acf-field-number.php:199
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:229
#: includes/fields/class-acf-field-text.php:148
msgid "Appears before the input"
msgstr "Prijazuje se ispred polja"

#: includes/fields/class-acf-field-email.php:147
#: includes/fields/class-acf-field-number.php:198
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:147
msgid "Prepend"
msgstr "Umetni ispred"

#: includes/fields/class-acf-field-email.php:138
#: includes/fields/class-acf-field-number.php:179
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-text.php:138
#: includes/fields/class-acf-field-textarea.php:156
#: includes/fields/class-acf-field-url.php:122
msgid "Appears within the input"
msgstr "Prikazuje se unutar polja"

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:178
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-text.php:137
#: includes/fields/class-acf-field-textarea.php:155
#: includes/fields/class-acf-field-url.php:121
msgid "Placeholder Text"
msgstr "Zadana vrijednost"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:129
#: includes/fields/class-acf-field-radio.php:203
#: includes/fields/class-acf-field-range.php:165
#: includes/fields/class-acf-field-text.php:98
#: includes/fields/class-acf-field-textarea.php:104
#: includes/fields/class-acf-field-url.php:102
#: includes/fields/class-acf-field-wysiwyg.php:319
msgid "Appears when creating a new post"
msgstr "Prikazuje se prilikom kreiranje nove objave"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Tekst"

#: includes/fields/class-acf-field-relationship.php:789
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/fields/class-acf-field-post_object.php:424
#: includes/fields/class-acf-field-relationship.php:651
msgid "Post ID"
msgstr "ID objave"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:423
#: includes/fields/class-acf-field-relationship.php:650
msgid "Post Object"
msgstr "Objekt"

#: includes/fields/class-acf-field-relationship.php:683
msgid "Maximum posts"
msgstr "Maksimalno"

#: includes/fields/class-acf-field-relationship.php:673
msgid "Minimum posts"
msgstr "Minimalno"

#: includes/admin/views/acf-field-group/options.php:168
#: includes/admin/views/acf-post-type/advanced-settings.php:25
#: includes/fields/class-acf-field-relationship.php:708
msgid "Featured Image"
msgstr "Istaknuta slika"

#: includes/fields/class-acf-field-relationship.php:704
msgid "Selected elements will be displayed in each result"
msgstr "Odabrani elementi bit će prikazani u svakom rezultatu"

#: includes/fields/class-acf-field-relationship.php:703
msgid "Elements"
msgstr "Elementi"

#: includes/fields/class-acf-field-relationship.php:637
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:709
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Taksonomija"

#: includes/fields/class-acf-field-relationship.php:636
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Type"
msgstr "Tip objave"

#: includes/fields/class-acf-field-relationship.php:630
msgid "Filters"
msgstr "Filteri"

#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-relationship.php:623
msgid "All taxonomies"
msgstr "Sve taksonomije"

#: includes/fields/class-acf-field-page_link.php:491
#: includes/fields/class-acf-field-post_object.php:403
#: includes/fields/class-acf-field-relationship.php:615
msgid "Filter by Taxonomy"
msgstr "Filtriraj prema taksonomiji"

#: includes/fields/class-acf-field-page_link.php:469
#: includes/fields/class-acf-field-post_object.php:381
#: includes/fields/class-acf-field-relationship.php:593
msgid "All post types"
msgstr "Svi tipovi"

#: includes/fields/class-acf-field-page_link.php:461
#: includes/fields/class-acf-field-post_object.php:373
#: includes/fields/class-acf-field-relationship.php:585
msgid "Filter by Post Type"
msgstr "Filtriraj po tipu posta"

#: includes/fields/class-acf-field-relationship.php:483
msgid "Search..."
msgstr "Pretraga…"

#: includes/fields/class-acf-field-relationship.php:413
msgid "Select taxonomy"
msgstr "Odebarite taksonomiju"

#: includes/fields/class-acf-field-relationship.php:404
msgid "Select post type"
msgstr "Odaberi tip posta"

#: includes/fields/class-acf-field-relationship.php:68
#: assets/build/js/acf-input.js:3925 assets/build/js/acf-input.js:4208
msgid "No matches found"
msgstr "Nema rezultata"

#: includes/fields/class-acf-field-relationship.php:67
#: assets/build/js/acf-input.js:3908 assets/build/js/acf-input.js:4187
msgid "Loading"
msgstr "Učitavanje"

#: includes/fields/class-acf-field-relationship.php:66
#: assets/build/js/acf-input.js:3817 assets/build/js/acf-input.js:4083
msgid "Maximum values reached ( {max} values )"
msgstr "Već ste dodali najviše dozvoljenih vrijednosti (najviše: {max})"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Veza"

#: includes/fields/class-acf-field-file.php:291
#: includes/fields/class-acf-field-image.php:317
msgid "Comma separated list. Leave blank for all types"
msgstr ""
"Dodaj kao niz odvojen zarezom, npr: .txt, .jpg, ... Ukoliko je prazno, sve "
"datoteke su dozvoljene"

#: includes/fields/class-acf-field-file.php:290
#: includes/fields/class-acf-field-image.php:316
msgid "Allowed file types"
msgstr "Dozvoljeni tipovi datoteka"

#: includes/fields/class-acf-field-file.php:278
#: includes/fields/class-acf-field-image.php:280
msgid "Maximum"
msgstr "Maksimum"

#: includes/fields/class-acf-field-file.php:154
#: includes/fields/class-acf-field-file.php:270
#: includes/fields/class-acf-field-file.php:282
#: includes/fields/class-acf-field-image.php:271
#: includes/fields/class-acf-field-image.php:307
msgid "File size"
msgstr "Veličina datoteke"

#: includes/fields/class-acf-field-image.php:245
#: includes/fields/class-acf-field-image.php:281
msgid "Restrict which images can be uploaded"
msgstr "Ograniči koje slike mogu biti dodane"

#: includes/fields/class-acf-field-file.php:266
#: includes/fields/class-acf-field-image.php:244
msgid "Minimum"
msgstr "Minimum"

#: includes/fields/class-acf-field-file.php:235
#: includes/fields/class-acf-field-image.php:210
msgid "Uploaded to post"
msgstr "Dodani uz trenutnu objavu"

#: includes/fields/class-acf-field-file.php:234
#: includes/fields/class-acf-field-image.php:209
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Sve"

#: includes/fields/class-acf-field-file.php:229
#: includes/fields/class-acf-field-image.php:204
msgid "Limit the media library choice"
msgstr "Ograniči odabir iz zbirke"

#: includes/fields/class-acf-field-file.php:228
#: includes/fields/class-acf-field-image.php:203
msgid "Library"
msgstr "Zbirka"

#: includes/fields/class-acf-field-image.php:336
msgid "Preview Size"
msgstr "Veličina prikaza prilikom uređivanja stranice"

#: includes/fields/class-acf-field-image.php:195
msgid "Image ID"
msgstr "ID slike"

#: includes/fields/class-acf-field-image.php:194
msgid "Image URL"
msgstr "Putanja slike"

#: includes/fields/class-acf-field-image.php:193
msgid "Image Array"
msgstr "Podaci kao niz"

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:372
#: includes/fields/class-acf-field-file.php:213
#: includes/fields/class-acf-field-link.php:171
#: includes/fields/class-acf-field-radio.php:213
msgid "Specify the returned value on front end"
msgstr "Vrijednost koja će biti vraćena na pristupnom dijelu"

#: includes/fields/class-acf-field-button-group.php:167
#: includes/fields/class-acf-field-checkbox.php:371
#: includes/fields/class-acf-field-file.php:212
#: includes/fields/class-acf-field-link.php:170
#: includes/fields/class-acf-field-radio.php:212
#: includes/fields/class-acf-field-taxonomy.php:753
msgid "Return Value"
msgstr "Vrati vrijednost"

#: includes/fields/class-acf-field-image.php:162
msgid "Add Image"
msgstr "Dodaj sliku"

#: includes/fields/class-acf-field-image.php:162
msgid "No image selected"
msgstr "Nema odabranih slika"

#: includes/assets.php:352 includes/fields/class-acf-field-file.php:162
#: includes/fields/class-acf-field-image.php:142
#: includes/fields/class-acf-field-link.php:145 assets/build/js/acf.js:1566
#: assets/build/js/acf.js:1661
msgid "Remove"
msgstr "Ukloni"

#: includes/admin/views/acf-field-group/field.php:72
#: includes/fields/class-acf-field-file.php:160
#: includes/fields/class-acf-field-image.php:140
#: includes/fields/class-acf-field-link.php:145
msgid "Edit"
msgstr "Uredi"

#: includes/fields/class-acf-field-image.php:70 includes/media.php:55
#: assets/build/js/acf-input.js:6813 assets/build/js/acf-input.js:7300
msgid "All images"
msgstr "Sve slike"

#: includes/fields/class-acf-field-image.php:69
#: assets/build/js/acf-input.js:3181 assets/build/js/acf-input.js:3399
msgid "Update Image"
msgstr "Ažuriraj sliku"

#: includes/fields/class-acf-field-image.php:68
#: assets/build/js/acf-input.js:3180 assets/build/js/acf-input.js:3398
msgid "Edit Image"
msgstr "Uredi sliku"

#: includes/fields/class-acf-field-image.php:67
#: assets/build/js/acf-input.js:3156 assets/build/js/acf-input.js:3373
msgid "Select Image"
msgstr "Odaberi sliku"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Slika"

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Prikažite HTML kodove kao tekst umjesto iscrtavanja"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr "Onemogući HTML"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:172
msgid "No Formatting"
msgstr "Bez obrade"

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:171
msgid "Automatically add &lt;br&gt;"
msgstr "Dodaj novi red - &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:114
#: includes/fields/class-acf-field-textarea.php:170
msgid "Automatically add paragraphs"
msgstr "Dodaj paragraf"

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:166
msgid "Controls how new lines are rendered"
msgstr "Određuje način prikaza novih linija"

#: includes/fields/class-acf-field-message.php:109
#: includes/fields/class-acf-field-textarea.php:165
msgid "New Lines"
msgstr "Broj linija"

#: includes/fields/class-acf-field-date_picker.php:232
#: includes/fields/class-acf-field-date_time_picker.php:220
msgid "Week Starts On"
msgstr "Tjedan počinje"

#: includes/fields/class-acf-field-date_picker.php:201
msgid "The format used when saving a value"
msgstr "Format koji će biti spremljen"

#: includes/fields/class-acf-field-date_picker.php:200
msgid "Save Format"
msgstr "Spremi format"

#: includes/fields/class-acf-field-date_picker.php:67
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Tjedan"

#: includes/fields/class-acf-field-date_picker.php:66
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Prethodni"

#: includes/fields/class-acf-field-date_picker.php:65
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Slijedeći"

#: includes/fields/class-acf-field-date_picker.php:64
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Danas"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Završeno"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Odabir datuma"

#: includes/fields/class-acf-field-image.php:248
#: includes/fields/class-acf-field-image.php:284
#: includes/fields/class-acf-field-oembed.php:268
msgid "Width"
msgstr "Širina"

#: includes/fields/class-acf-field-oembed.php:265
#: includes/fields/class-acf-field-oembed.php:277
msgid "Embed Size"
msgstr "Dimenzija umetka"

#: includes/fields/class-acf-field-oembed.php:222
msgid "Enter URL"
msgstr "Poveznica"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:184
msgid "Text shown when inactive"
msgstr "Tekst prikazan dok je polje neaktivno"

#: includes/fields/class-acf-field-true_false.php:183
msgid "Off Text"
msgstr "Tekst za neaktivno stanje"

#: includes/fields/class-acf-field-true_false.php:168
msgid "Text shown when active"
msgstr "Tekst prikazan dok je polje aktivno"

#: includes/fields/class-acf-field-true_false.php:167
msgid "On Text"
msgstr "Tekst za aktivno stanje"

#: includes/fields/class-acf-field-select.php:456
#: includes/fields/class-acf-field-true_false.php:199
msgid "Stylized UI"
msgstr "Stilizirano sučelje"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:361
#: includes/fields/class-acf-field-color_picker.php:158
#: includes/fields/class-acf-field-email.php:117
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-range.php:164
#: includes/fields/class-acf-field-select.php:386
#: includes/fields/class-acf-field-text.php:97
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-true_false.php:147
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:318
msgid "Default Value"
msgstr "Zadana vrijednost"

#: includes/fields/class-acf-field-true_false.php:138
msgid "Displays text alongside the checkbox"
msgstr "Prikazuje tekst uz odabirni okvir"

#: includes/fields/class-acf-field-message.php:26
#: includes/fields/class-acf-field-message.php:99
#: includes/fields/class-acf-field-true_false.php:137
msgid "Message"
msgstr "Poruka"

#: includes/assets.php:351 includes/fields/class-acf-field-true_false.php:86
#: includes/fields/class-acf-field-true_false.php:187
#: assets/build/js/acf.js:1743 assets/build/js/acf.js:1861
msgid "No"
msgstr "Ne"

#: includes/assets.php:350 includes/fields/class-acf-field-true_false.php:83
#: includes/fields/class-acf-field-true_false.php:171
#: assets/build/js/acf.js:1742 assets/build/js/acf.js:1860
msgid "Yes"
msgstr "Da"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "True / False"

#: includes/fields/class-acf-field-group.php:474
msgid "Row"
msgstr "Red"

#: includes/fields/class-acf-field-group.php:473
msgid "Table"
msgstr "Tablica"

#: includes/admin/post-types/admin-field-group.php:131
#: includes/fields/class-acf-field-group.php:472
msgid "Block"
msgstr "Blok"

#: includes/fields/class-acf-field-group.php:467
msgid "Specify the style used to render the selected fields"
msgstr "Odaberite način prikaza odabranih polja"

#: includes/fields.php:356 includes/fields/class-acf-field-button-group.php:215
#: includes/fields/class-acf-field-checkbox.php:435
#: includes/fields/class-acf-field-group.php:466
#: includes/fields/class-acf-field-radio.php:286
msgid "Layout"
msgstr "Format"

#: includes/fields/class-acf-field-group.php:450
msgid "Sub Fields"
msgstr "Pod polja"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Skup polja"

#: includes/fields/class-acf-field-google-map.php:235
msgid "Customize the map height"
msgstr ""

#: includes/fields/class-acf-field-google-map.php:234
#: includes/fields/class-acf-field-image.php:259
#: includes/fields/class-acf-field-image.php:295
#: includes/fields/class-acf-field-oembed.php:280
msgid "Height"
msgstr "Visina"

#: includes/fields/class-acf-field-google-map.php:223
msgid "Set the initial zoom level"
msgstr "Postavi zadanu vrijednost uvećanja"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Zoom"
msgstr "Uvećaj"

#: includes/fields/class-acf-field-google-map.php:196
#: includes/fields/class-acf-field-google-map.php:209
msgid "Center the initial map"
msgstr "Centriraj prilikom učitavanja"

#: includes/fields/class-acf-field-google-map.php:195
#: includes/fields/class-acf-field-google-map.php:208
msgid "Center"
msgstr "Centriraj"

#: includes/fields/class-acf-field-google-map.php:163
msgid "Search for address..."
msgstr "Pretraži po adresi..."

#: includes/fields/class-acf-field-google-map.php:160
msgid "Find current location"
msgstr "Pronađi trenutnu lokaciju"

#: includes/fields/class-acf-field-google-map.php:159
msgid "Clear location"
msgstr "Ukloni lokaciju"

#: includes/fields/class-acf-field-google-map.php:158
#: includes/fields/class-acf-field-relationship.php:635
msgid "Search"
msgstr "Pretraži"

#: includes/fields/class-acf-field-google-map.php:63
#: assets/build/js/acf-input.js:2840 assets/build/js/acf-input.js:3026
msgid "Sorry, this browser does not support geolocation"
msgstr "Nažalost, ovaj preglednik ne podržava geo lociranje"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Google mapa"

#: includes/fields/class-acf-field-date_picker.php:212
#: includes/fields/class-acf-field-date_time_picker.php:201
#: includes/fields/class-acf-field-time_picker.php:132
msgid "The format returned via template functions"
msgstr "Format koji vraća funkcija"

#: includes/fields/class-acf-field-color_picker.php:182
#: includes/fields/class-acf-field-date_picker.php:211
#: includes/fields/class-acf-field-date_time_picker.php:200
#: includes/fields/class-acf-field-image.php:187
#: includes/fields/class-acf-field-post_object.php:418
#: includes/fields/class-acf-field-relationship.php:645
#: includes/fields/class-acf-field-select.php:397
#: includes/fields/class-acf-field-time_picker.php:131
#: includes/fields/class-acf-field-user.php:70
msgid "Return Format"
msgstr "Format za prikaz na web stranici"

#: includes/fields/class-acf-field-date_picker.php:190
#: includes/fields/class-acf-field-date_picker.php:221
#: includes/fields/class-acf-field-date_time_picker.php:192
#: includes/fields/class-acf-field-date_time_picker.php:210
#: includes/fields/class-acf-field-time_picker.php:123
#: includes/fields/class-acf-field-time_picker.php:139
msgid "Custom:"
msgstr "Prilagođeno:"

#: includes/fields/class-acf-field-date_picker.php:182
#: includes/fields/class-acf-field-date_time_picker.php:183
#: includes/fields/class-acf-field-time_picker.php:116
msgid "The format displayed when editing a post"
msgstr "Format za prikaz prilikom administracije"

#: includes/fields/class-acf-field-date_picker.php:181
#: includes/fields/class-acf-field-date_time_picker.php:182
#: includes/fields/class-acf-field-time_picker.php:115
msgid "Display Format"
msgstr "Format prikaza"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Odabri vremena (sat i minute)"

#. translators: counts for inactive field groups
#: acf.php:491
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: acf.php:450
msgid "No Fields found in Trash"
msgstr "Nije pronađeno nijedno polje u smeću"

#: acf.php:449
msgid "No Fields found"
msgstr "Nije pronađeno nijedno polje"

#: acf.php:448
msgid "Search Fields"
msgstr "Pretraži polja"

#: acf.php:447
msgid "View Field"
msgstr "Pregledaj polje"

#: acf.php:446 includes/admin/views/acf-field-group/fields.php:104
msgid "New Field"
msgstr "Dodaj polje"

#: acf.php:445
msgid "Edit Field"
msgstr "Uredi polje"

#: acf.php:444
msgid "Add New Field"
msgstr "Dodaj polje"

#: acf.php:442
msgid "Field"
msgstr "Polje"

#: acf.php:441 includes/admin/post-types/admin-field-group.php:154
#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/views/acf-field-group/fields.php:21
msgid "Fields"
msgstr "Polja"

#: acf.php:416
msgid "No Field Groups found in Trash"
msgstr "Nije pronađena nijedna stranica"

#: acf.php:415
msgid "No Field Groups found"
msgstr "Niste dodali nijedno polje"

#: acf.php:414
msgid "Search Field Groups"
msgstr "Pretraži polja"

#: acf.php:413
msgid "View Field Group"
msgstr "Pregledaj polje"

#: acf.php:412
msgid "New Field Group"
msgstr "Novo polje"

#: acf.php:411
msgid "Edit Field Group"
msgstr "Uredi polje"

#: acf.php:410
msgid "Add New Field Group"
msgstr "Dodaj novo polje"

#: acf.php:409 acf.php:443
#: includes/admin/views/acf-post-type/advanced-settings.php:215
#: includes/admin/views/acf-post-type/advanced-settings.php:217
#: includes/post-types/class-acf-post-type.php:92
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "Dodaj"

#: acf.php:408
msgid "Field Group"
msgstr "Grupa polja"

#: acf.php:407 includes/admin/post-types/admin-field-groups.php:56
#: includes/admin/post-types/admin-post-types.php:105
#: includes/admin/post-types/admin-taxonomies.php:105
msgid "Field Groups"
msgstr "Grupe polja"

#. Description of the plugin
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""

#. Plugin URI of the plugin
msgid "https://www.advancedcustomfields.com"
msgstr ""

#. Plugin Name of the plugin
#: acf.php:92
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/blocks.php:170
#, fuzzy
#| msgid "%s value is required"
msgid "Block type name is required."
msgstr "%s je obavezno"

#. translators: The name of the block type
#: pro/blocks.php:178
msgid "Block type \"%s\" is already registered."
msgstr ""

#: pro/blocks.php:726
msgid "Switch to Edit"
msgstr ""

#: pro/blocks.php:727
msgid "Switch to Preview"
msgstr ""

#: pro/blocks.php:728
msgid "Change content alignment"
msgstr ""

#. translators: %s: Block type title
#: pro/blocks.php:731
#, fuzzy
#| msgid "Settings"
msgid "%s settings"
msgstr "Postavke"

#: pro/blocks.php:936
msgid "This block contains no editable fields."
msgstr ""

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:942
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "Postavke spremljene"

#: pro/updates.php:99
#, fuzzy
#| msgid ""
#| "To enable updates, please enter your license key on the <a "
#| "href=\"%s\">Updates</a> page. If you don't have a licence key, please see "
#| "<a href=\"%s\">details & pricing</a>."
msgid ""
"To enable updates, please enter your license key on the <a "
"href=\"%1$s\">Updates</a> page. If you don't have a licence key, please see "
"<a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"Da bi omogućili automatsko ažuriranje, molimo unesite licencu na stranici <a "
"href=“%s”>ažuriranja</a>. Ukoliko nemate licencu, pogledajte <a "
"href=“%s”>opcije i cijene</a>."

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr ""

#: pro/updates.php:187
#, fuzzy
#| msgid "<b>Error</b>. Could not connect to update server"
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr "<b>Greška</b>. Greška prilikom spajanja na server"

#: pro/updates.php:279
msgid "Check Again"
msgstr "Provjeri ponovno"

#: pro/updates.php:593
#, fuzzy
#| msgid "<b>Error</b>. Could not connect to update server"
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr "<b>Greška</b>. Greška prilikom spajanja na server"

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "Objavi"

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Niste dodali nijedan skup polja na ovu stranicu, <a href=\"%s\">Dodaj skup "
"polja</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Greška</b>. Greška prilikom spajanja na server"

#: pro/admin/admin-updates.php:212
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""

#: pro/admin/admin-updates.php:199
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""

#: pro/fields/class-acf-field-clone.php:27,
#: pro/fields/class-acf-field-repeater.php:31
msgid ""
"Allows you to select and display existing fields. It does not duplicate any "
"fields in the database, but loads and displays the selected fields at run-"
"time. The Clone field can either replace itself with the selected fields or "
"display the selected fields as a group of subfields."
msgstr ""

#: pro/fields/class-acf-field-clone.php:819
msgid "Select one or more fields you wish to clone"
msgstr "Odaberite jedno ili više polja koja želite klonirati"

#: pro/fields/class-acf-field-clone.php:838
msgid "Display"
msgstr "Prikaz"

#: pro/fields/class-acf-field-clone.php:839
msgid "Specify the style used to render the clone field"
msgstr "Odaberite način prikaza kloniranog polja"

#: pro/fields/class-acf-field-clone.php:844
msgid "Group (displays selected fields in a group within this field)"
msgstr ""
"Skupno (Prikazuje odabrana polja kao dodatni skup unutar trenutnog polja)"

#: pro/fields/class-acf-field-clone.php:845
msgid "Seamless (replaces this field with selected fields)"
msgstr "Zamjena (Prikazuje odabrana polja umjesto trenutnog polja)"

#: pro/fields/class-acf-field-clone.php:868
msgid "Labels will be displayed as %s"
msgstr "Oznake će biti prikazane kao %s"

#: pro/fields/class-acf-field-clone.php:873
msgid "Prefix Field Labels"
msgstr "Dodaj prefiks ispred oznake"

#: pro/fields/class-acf-field-clone.php:883
msgid "Values will be saved as %s"
msgstr "Vrijednosti će biti spremljene kao %s"

#: pro/fields/class-acf-field-clone.php:888
msgid "Prefix Field Names"
msgstr "Dodaj prefiks ispred naziva polja"

#: pro/fields/class-acf-field-clone.php:1005
msgid "Unknown field"
msgstr "Nepoznato polje"

#: pro/fields/class-acf-field-clone.php:1042
msgid "Unknown field group"
msgstr "Nepoznat skup polja"

#: pro/fields/class-acf-field-clone.php:1046
msgid "All fields from %s field group"
msgstr "Sva polje iz %s skupa polja"

#: pro/fields/class-acf-field-flexible-content.php:27
msgid ""
"Allows you to define, create and manage content with total control by "
"creating layouts that contain subfields that content editors can choose from."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:36,
#: pro/fields/class-acf-field-repeater.php:103,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Add Row"
msgstr "Dodaj red"

#: pro/fields/class-acf-field-flexible-content.php:76,
#: pro/fields/class-acf-field-flexible-content.php:943,
#: pro/fields/class-acf-field-flexible-content.php:1022
#, fuzzy
#| msgid "layout"
msgid "layout"
msgid_plural "layouts"
msgstr[0] "raspored"
msgstr[1] "raspored"
msgstr[2] "raspored"

#: pro/fields/class-acf-field-flexible-content.php:77
msgid "layouts"
msgstr "rasporedi"

#: pro/fields/class-acf-field-flexible-content.php:81,
#: pro/fields/class-acf-field-flexible-content.php:942,
#: pro/fields/class-acf-field-flexible-content.php:1021
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Polje mora sadržavati najmanje {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:82
#, fuzzy
#| msgid "This field has a limit of {max} {identifier}"
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Polje je ograničeno na najviše {max} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} preostalo (najviše {max})"

#: pro/fields/class-acf-field-flexible-content.php:86
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} obavezno (najmanje {min})"

#: pro/fields/class-acf-field-flexible-content.php:89
msgid "Flexible Content requires at least 1 layout"
msgstr "Potrebno je unijeti najmanje jedno fleksibilni polje"

#: pro/fields/class-acf-field-flexible-content.php:282
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Kliknite “%s” gumb kako bi započeki kreiranje raspored"

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "Dodaj razmještaj"

#: pro/fields/class-acf-field-flexible-content.php:424
#, fuzzy
#| msgid "Duplicate Layout"
msgid "Duplicate layout"
msgstr "Dupliciraj razmještaj"

#: pro/fields/class-acf-field-flexible-content.php:425
msgid "Remove layout"
msgstr "Ukloni razmještaj"

#: pro/fields/class-acf-field-flexible-content.php:426,
#: pro/fields/class-acf-repeater-table.php:382
msgid "Click to toggle"
msgstr "Klikni za uključivanje/isključivanje"

#: pro/fields/class-acf-field-flexible-content.php:562
msgid "Delete Layout"
msgstr "Obriši"

#: pro/fields/class-acf-field-flexible-content.php:563
msgid "Duplicate Layout"
msgstr "Dupliciraj razmještaj"

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add New Layout"
msgstr "Dodaj novi razmještaj"

#: pro/fields/class-acf-field-flexible-content.php:564
#, fuzzy
#| msgid "Add layout"
msgid "Add Layout"
msgstr "Dodaj razmještaj"

#: pro/fields/class-acf-field-flexible-content.php:647
msgid "Min"
msgstr "Minimum"

#: pro/fields/class-acf-field-flexible-content.php:662
msgid "Max"
msgstr "Maksimum"

#: pro/fields/class-acf-field-flexible-content.php:705
msgid "Minimum Layouts"
msgstr "Najmanje"

#: pro/fields/class-acf-field-flexible-content.php:716
msgid "Maximum Layouts"
msgstr "Najviše"

#: pro/fields/class-acf-field-flexible-content.php:727,
#: pro/fields/class-acf-field-repeater.php:293
msgid "Button Label"
msgstr "Tekst gumba"

#: pro/fields/class-acf-field-flexible-content.php:1710,
#: pro/fields/class-acf-field-repeater.php:918
msgid "%s must be of type array or null."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1721
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: pro/fields/class-acf-field-flexible-content.php:1737
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: pro/fields/class-acf-field-gallery.php:27
msgid ""
"An interactive interface for managing a collection of attachments, such as "
"images."
msgstr ""

#: pro/fields/class-acf-field-gallery.php:77
msgid "Add Image to Gallery"
msgstr "Dodaj sliku u galeriju"

#: pro/fields/class-acf-field-gallery.php:78
msgid "Maximum selection reached"
msgstr "Već ste dodali najviše dozovoljenih polja"

#: pro/fields/class-acf-field-gallery.php:324
msgid "Length"
msgstr "Dužina"

#: pro/fields/class-acf-field-gallery.php:368
msgid "Caption"
msgstr "Potpis"

#: pro/fields/class-acf-field-gallery.php:380
msgid "Alt Text"
msgstr "Alternativni tekst"

#: pro/fields/class-acf-field-gallery.php:504
msgid "Add to gallery"
msgstr "Dodaj u galeriju"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Bulk actions"
msgstr "Grupne akcije"

#: pro/fields/class-acf-field-gallery.php:509
msgid "Sort by date uploaded"
msgstr "Razvrstaj po datumu dodavanja"

#: pro/fields/class-acf-field-gallery.php:510
msgid "Sort by date modified"
msgstr "Razvrstaj po datumu zadnje promjene"

#: pro/fields/class-acf-field-gallery.php:511
msgid "Sort by title"
msgstr "Razvrstaj po naslovu"

#: pro/fields/class-acf-field-gallery.php:512
msgid "Reverse current order"
msgstr "Obrnuti redosljed"

#: pro/fields/class-acf-field-gallery.php:524
msgid "Close"
msgstr "Zatvori"

#: pro/fields/class-acf-field-gallery.php:615
msgid "Minimum Selection"
msgstr "Minimalni odabri"

#: pro/fields/class-acf-field-gallery.php:625
msgid "Maximum Selection"
msgstr "Maksimalni odabir"

#: pro/fields/class-acf-field-gallery.php:727
msgid "Insert"
msgstr "Umetni"

#: pro/fields/class-acf-field-gallery.php:728
msgid "Specify where new attachments are added"
msgstr "Precizirajte gdje se dodaju novi prilozi"

#: pro/fields/class-acf-field-gallery.php:732
msgid "Append to the end"
msgstr "Umetni na kraj"

#: pro/fields/class-acf-field-gallery.php:733
msgid "Prepend to the beginning"
msgstr "Umetni na početak"

#: pro/fields/class-acf-field-repeater.php:66,
#: pro/fields/class-acf-field-repeater.php:463
#, fuzzy
#| msgid "Minimum rows reached ({min} rows)"
msgid "Minimum rows not reached ({min} rows)"
msgstr "Minimalni broj redova je već odabran ({min})"

#: pro/fields/class-acf-field-repeater.php:67
msgid "Maximum rows reached ({max} rows)"
msgstr "Maksimalni broj redova je već odabran ({max})"

#: pro/fields/class-acf-field-repeater.php:68
#, fuzzy
#| msgctxt "Select2 JS load_fail"
#| msgid "Loading failed"
msgid "Error loading page"
msgstr "Neuspješno učitavanje"

#: pro/fields/class-acf-field-repeater.php:69
msgid "Order will be assigned upon save"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:196
msgid "Useful for fields with a large number of rows."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:207
#, fuzzy
#| msgid "Posts Page"
msgid "Rows Per Page"
msgstr "Stranica za objave"

#: pro/fields/class-acf-field-repeater.php:208
#, fuzzy
#| msgid "Select the taxonomy to be displayed"
msgid "Set the number of rows to be displayed on a page."
msgstr "Odaberite taksonomiju za prikaz"

#: pro/fields/class-acf-field-repeater.php:240
msgid "Minimum Rows"
msgstr "Minimalno redova"

#: pro/fields/class-acf-field-repeater.php:251
msgid "Maximum Rows"
msgstr "Maksimalno redova"

#: pro/fields/class-acf-field-repeater.php:281
msgid "Collapsed"
msgstr "Sklopljeno"

#: pro/fields/class-acf-field-repeater.php:282
msgid "Select a sub field to show when row is collapsed"
msgstr "Odaberite pod polje koje će biti prikazano dok je red sklopljen"

#: pro/fields/class-acf-field-repeater.php:1060
#, fuzzy
#| msgid "Edit field group"
msgid "Invalid field key or name."
msgstr "Uredi skup polja"

#: pro/fields/class-acf-field-repeater.php:1069
msgid "There was an error retrieving the field."
msgstr ""

#: pro/fields/class-acf-repeater-table.php:369
#, fuzzy
#| msgid "Drag to reorder"
msgid "Click to reorder"
msgstr "Presloži polja povlačenjem"

#: pro/fields/class-acf-repeater-table.php:402
msgid "Add row"
msgstr "Dodaj red"

#: pro/fields/class-acf-repeater-table.php:403
#, fuzzy
#| msgid "Duplicate"
msgid "Duplicate row"
msgstr "Dupliciraj"

#: pro/fields/class-acf-repeater-table.php:404
msgid "Remove row"
msgstr "Ukloni red"

#: pro/fields/class-acf-repeater-table.php:448,
#: pro/fields/class-acf-repeater-table.php:465,
#: pro/fields/class-acf-repeater-table.php:466
#, fuzzy
#| msgid "Current User"
msgid "Current Page"
msgstr "Trenutni korisnik"

#: pro/fields/class-acf-repeater-table.php:456,
#: pro/fields/class-acf-repeater-table.php:457
#, fuzzy
#| msgid "Front Page"
msgid "First Page"
msgstr "Početna stranica"

#: pro/fields/class-acf-repeater-table.php:460,
#: pro/fields/class-acf-repeater-table.php:461
#, fuzzy
#| msgid "Posts Page"
msgid "Previous Page"
msgstr "Stranica za objave"

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:470
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:477,
#: pro/fields/class-acf-repeater-table.php:478
#, fuzzy
#| msgid "Front Page"
msgid "Next Page"
msgstr "Početna stranica"

#: pro/fields/class-acf-repeater-table.php:481,
#: pro/fields/class-acf-repeater-table.php:482
#, fuzzy
#| msgid "Posts Page"
msgid "Last Page"
msgstr "Stranica za objave"

#: pro/locations/class-acf-location-block.php:71
#, fuzzy
#| msgid "No options pages exist"
msgid "No block types exist"
msgstr "Ne postoji stranica sa postavkama"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Ne postoji stranica sa postavkama"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Deaktiviraj licencu"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Aktiviraj licencu"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Informacije o licenci"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Da bi omogućili ažuriranje, molimo unesite vašu licencu i polje ispod. "
"Ukoliko ne posjedujete licencu, molimo posjetite <a href=“%s” "
"target=“_blank”>detalji i cijene</a>."

#: pro/admin/views/html-settings-updates.php:37
msgid "License Key"
msgstr "Licenca"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr ""

#: pro/admin/views/html-settings-updates.php:29
#, fuzzy
#| msgid "Better Validation"
msgid "Retry Activation"
msgstr "Bolja verifikacija polja"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Ažuriraj informacije"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Trenutna vezija"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Posljednja dostupna verzija"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Dostupna nadogradnja"

#: pro/admin/views/html-settings-updates.php:98
msgid "Upgrade Notice"
msgstr "Obavijest od nadogradnjama"

#: pro/admin/views/html-settings-updates.php:126
msgid "Check For Updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:121
#, fuzzy
#| msgid "Please enter your license key above to unlock updates"
msgid "Enter your license key to unlock updates"
msgstr "Unesite licencu kako bi mogli izvršiti nadogradnju"

#: pro/admin/views/html-settings-updates.php:119
msgid "Update Plugin"
msgstr "Nadogradi dodatak"

#: pro/admin/views/html-settings-updates.php:117
#, fuzzy
#| msgid "Please enter your license key above to unlock updates"
msgid "Please reactivate your license to unlock updates"
msgstr "Unesite licencu kako bi mogli izvršiti nadogradnju"
