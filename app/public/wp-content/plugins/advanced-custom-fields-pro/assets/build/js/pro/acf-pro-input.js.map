{"version": 3, "file": "acf-pro-input.js", "mappings": ";;;;;;;;;AAAA,CAAE,UAAWA,CAAC,EAAG;EAChB,IAAIC,KAAK,GAAGC,GAAG,CAACD,KAAK,CAACE,MAAM,CAAE;IAC7BC,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,EAAE;IAERC,MAAM,EAAE;MACP,gCAAgC,EAAE,YAAY;MAC9C,sCAAsC,EAAE,kBAAkB;MAC1D,mCAAmC,EAAE,eAAe;MACpD,qCAAqC,EAAE,iBAAiB;MACxDC,SAAS,EAAE,QAAQ;MACnBC,WAAW,EAAE,UAAU;MACvBC,SAAS,EAAE;IACZ,CAAC;IAEDC,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,OAAO,IAAI,CAACV,CAAC,CAAE,6BAA6B,CAAE;IAC/C,CAAC;IAEDW,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB,OAAO,IAAI,CAACX,CAAC,CAAE,uCAAuC,CAAE;IACzD,CAAC;IAEDY,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,OAAO,IAAI,CAACZ,CAAC,CAAE,iDAAiD,CAAE;IACnE,CAAC;IAEDa,OAAO,EAAE,SAAAA,CAAWC,KAAK,EAAG;MAC3B,OAAO,IAAI,CAACd,CAAC,CACZ,qDAAqD,GACpDc,KAAK,GACL,GAAG,CACJ;IACF,CAAC;IAEDC,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB,OAAO,IAAI,CAACf,CAAC,CAAE,uCAAuC,CAAE;IACzD,CAAC;IAEDgB,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB,OAAO,IAAI,CAAChB,CAAC,CAAE,kDAAkD,CAAE;IACpE,CAAC;IAEDiB,MAAM,EAAE,SAAAA,CAAWC,IAAI,EAAG;MACzB,OAAO,IAAI,CAAClB,CAAC,CACZ,gEAAgE,GAC/DkB,IAAI,GACJ,IAAI,CACL;IACF,CAAC;IAEDC,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,OAAO,IAAI,CAACnB,CAAC,CAAE,mBAAmB,CAAE;IACrC,CAAC;IAEDoB,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB,OAAO,IAAI,CAACpB,CAAC,CAAE,2BAA2B,CAAE;IAC7C,CAAC;IAEDqB,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,OAAO,IAAI,CAACrB,CAAC,CAAE,kBAAkB,CAAE;IACpC,CAAC;IAEDsB,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB,IAAIC,IAAI,GAAG,IAAI,CAACF,MAAM,EAAE,CAACE,IAAI,EAAE;MAC/B,IAAIC,KAAK,GAAGxB,CAAC,CAAEuB,IAAI,CAAE;;MAErB;MACA,IAAIX,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;MAC9B,IAAIa,YAAY,GAAG,SAAAA,CAAWP,IAAI,EAAG;QACpC,OAAON,QAAQ,CAACc,MAAM,CAAE,YAAY;UACnC,OAAO1B,CAAC,CAAE,IAAI,CAAE,CAAC2B,IAAI,CAAE,QAAQ,CAAE,KAAKT,IAAI;QAC3C,CAAC,CAAE,CAACU,MAAM;MACX,CAAC;;MAED;MACAJ,KAAK,CAACK,IAAI,CAAE,eAAe,CAAE,CAACC,IAAI,CAAE,YAAY;QAC/C,IAAIC,EAAE,GAAG/B,CAAC,CAAE,IAAI,CAAE;QAClB,IAAIgC,GAAG,GAAGD,EAAE,CAACJ,IAAI,CAAE,KAAK,CAAE,IAAI,CAAC;QAC/B,IAAIM,GAAG,GAAGF,EAAE,CAACJ,IAAI,CAAE,KAAK,CAAE,IAAI,CAAC;QAC/B,IAAIT,IAAI,GAAGa,EAAE,CAACJ,IAAI,CAAE,QAAQ,CAAE,IAAI,EAAE;QACpC,IAAIO,KAAK,GAAGT,YAAY,CAAEP,IAAI,CAAE;;QAEhC;QACA,IAAKe,GAAG,IAAIC,KAAK,IAAID,GAAG,EAAG;UAC1BF,EAAE,CAACI,QAAQ,CAAE,UAAU,CAAE;UACzB;QACD;;QAEA;QACA,IAAKH,GAAG,IAAIE,KAAK,GAAGF,GAAG,EAAG;UACzB,IAAII,QAAQ,GAAGJ,GAAG,GAAGE,KAAK;UAC1B,IAAIG,KAAK,GAAGnC,GAAG,CAACoC,EAAE,CACjB,sDAAsD,CACtD;UACD,IAAIC,UAAU,GAAGrC,GAAG,CAACsC,EAAE,CAAE,QAAQ,EAAE,SAAS,EAAEJ,QAAQ,CAAE;;UAExD;UACAC,KAAK,GAAGA,KAAK,CAACI,OAAO,CAAE,YAAY,EAAEL,QAAQ,CAAE;UAC/CC,KAAK,GAAGA,KAAK,CAACI,OAAO,CAAE,SAAS,EAAEvB,IAAI,CAAE,CAAC,CAAC;UAC1CmB,KAAK,GAAGA,KAAK,CAACI,OAAO,CAAE,cAAc,EAAEF,UAAU,CAAE;UACnDF,KAAK,GAAGA,KAAK,CAACI,OAAO,CAAE,OAAO,EAAET,GAAG,CAAE;;UAErC;UACAD,EAAE,CAACW,MAAM,CACR,6BAA6B,GAC5BL,KAAK,GACL,IAAI,GACJD,QAAQ,GACR,SAAS,CACV;QACF;MACD,CAAC,CAAE;;MAEH;MACAb,IAAI,GAAGC,KAAK,CAACmB,SAAS,EAAE;MAExB,OAAOpB,IAAI;IACZ,CAAC;IAEDqB,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,OAAO,IAAI,CAAChC,QAAQ,EAAE,CAACgB,MAAM;IAC9B,CAAC;IAEDiB,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB,IAAIb,GAAG,GAAGc,QAAQ,CAAE,IAAI,CAACC,GAAG,CAAE,KAAK,CAAE,CAAE;MACvC,OAAO,CAAEf,GAAG,IAAIA,GAAG,GAAG,IAAI,CAACgB,GAAG,EAAE;IACjC,CAAC;IAEDC,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,IAAIhB,GAAG,GAAGa,QAAQ,CAAE,IAAI,CAACC,GAAG,CAAE,KAAK,CAAE,CAAE;MACvC,OAAO,CAAEd,GAAG,IAAIA,GAAG,GAAG,IAAI,CAACe,GAAG,EAAE;IACjC,CAAC;IAEDE,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,IAAIjB,GAAG,GAAGa,QAAQ,CAAE,IAAI,CAACC,GAAG,CAAE,KAAK,CAAE,CAAE;MACvC,OAAOd,GAAG,IAAI,IAAI,CAACe,GAAG,EAAE,IAAIf,GAAG;IAChC,CAAC;IAEDkB,WAAW,EAAE,SAAAA,CAAWC,IAAI,EAAG;MAC9B;MACA,IAAK,IAAI,CAACL,GAAG,CAAE,KAAK,CAAE,IAAI,CAAC,EAAG;QAC7B;MACD;;MAEA;MACA,IAAI,CAACpC,YAAY,EAAE,CAAC0C,QAAQ,CAAE;QAC7BC,KAAK,EAAE,WAAW;QAClBC,MAAM,EAAE,yBAAyB;QACjCC,eAAe,EAAE,IAAI;QACrBC,oBAAoB,EAAE,IAAI;QAC1BC,MAAM,EAAE,IAAI;QACZC,IAAI,EAAE,SAAAA,CAAWC,KAAK,EAAEC,EAAE,EAAG;UAC5BT,IAAI,CAACU,MAAM,EAAE;QACd,CAAC;QACDC,MAAM,EAAE,SAAAA,CAAWH,KAAK,EAAEC,EAAE,EAAG;UAC9BT,IAAI,CAACY,MAAM,EAAE,CAACC,OAAO,CAAE,QAAQ,CAAE;QAClC;MACD,CAAC,CAAE;IACJ,CAAC;IAEDC,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB,IAAIC,OAAO,GAAGC,UAAU,CAACC,IAAI,CAAE,IAAI,CAACtB,GAAG,CAAE,KAAK,CAAE,CAAE;;MAElD;MACA,IAAK,CAAEoB,OAAO,EAAG;QAChB,OAAO,KAAK;MACb;;MAEA;MACA,IAAI,CAACvD,QAAQ,EAAE,CAACkB,IAAI,CAAE,UAAWwC,CAAC,EAAG;QACpC,IAAKH,OAAO,CAACI,OAAO,CAAED,CAAC,CAAE,GAAG,CAAC,CAAC,EAAG;UAChCtE,CAAC,CAAE,IAAI,CAAE,CAACmC,QAAQ,CAAE,YAAY,CAAE;QACnC;MACD,CAAC,CAAE;IACJ,CAAC;IAEDqC,iBAAiB,EAAE,SAAAA,CAAWpB,IAAI,EAAG;MACpC;MACA,IAAI,CAACqB,EAAE,CAAE,cAAc,EAAE,SAAS,EAAE,UAAWC,CAAC,EAAG;QAClDtB,IAAI,CAACuB,cAAc,CAAED,CAAC,EAAE1E,CAAC,CAAE,IAAI,CAAE,CAAE;MACpC,CAAC,CAAE;IACJ,CAAC;IAED4E,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB;MACA,IAAI,CAACJ,iBAAiB,CAAE,IAAI,CAAE;;MAE9B;MACA,IAAI,CAACN,YAAY,EAAE;;MAEnB;MACAhE,GAAG,CAAC2E,OAAO,CAAE,IAAI,CAAC9D,WAAW,EAAE,EAAE,IAAI,CAAC+D,GAAG,CAAE;;MAE3C;MACA,IAAI,CAAChB,MAAM,EAAE;IACd,CAAC;IAEDA,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB;MACA,IAAI,CAAClD,QAAQ,EAAE,CAACkB,IAAI,CAAE,UAAWwC,CAAC,EAAG;QACpCtE,CAAC,CAAE,IAAI,CAAE,CACP6B,IAAI,CAAE,4BAA4B,CAAE,CACpCN,IAAI,CAAE+C,CAAC,GAAG,CAAC,CAAE;MAChB,CAAC,CAAE;;MAEH;MACA,IAAK,IAAI,CAACtB,GAAG,EAAE,IAAI,CAAC,EAAG;QACtB,IAAI,CAACtC,QAAQ,EAAE,CAACyB,QAAQ,CAAE,QAAQ,CAAE;MACrC,CAAC,MAAM;QACN,IAAI,CAACzB,QAAQ,EAAE,CAACqE,WAAW,CAAE,QAAQ,CAAE;MACxC;;MAEA;MACA,IAAK,IAAI,CAAC7B,MAAM,EAAE,EAAG;QACpB,IAAI,CAAC9B,OAAO,EAAE,CAACe,QAAQ,CAAE,UAAU,CAAE;MACtC,CAAC,MAAM;QACN,IAAI,CAACf,OAAO,EAAE,CAAC2D,WAAW,CAAE,UAAU,CAAE;MACzC;IACD,CAAC;IAEDC,MAAM,EAAE,SAAAA,CAAWN,CAAC,EAAEO,GAAG,EAAEC,OAAO,EAAG;MACpC;MACA,IAAIC,MAAM,GAAGjF,GAAG,CAACkF,SAAS,CAAE;QAC3BC,EAAE,EAAE,UAAU;QACdC,MAAM,EAAE,IAAI,CAACL;MACd,CAAC,CAAE;;MAEH;MACA;MACA;MACA/E,GAAG,CAACqF,QAAQ,CAAE,aAAa,EAAEJ,MAAM,CAAE;IACtC,CAAC;IAEDK,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB;MACA,IAAK,IAAI,CAACvC,QAAQ,EAAE,EAAG;QACtB,OAAO,IAAI;MACZ;MAEA,IAAIhB,GAAG,GAAG,IAAI,CAACc,GAAG,CAAE,KAAK,CAAE;MAC3B,IAAI0C,IAAI,GAAGvF,GAAG,CAACoC,EAAE,CAChB,sDAAsD,CACtD;MACD,IAAIC,UAAU,GAAGrC,GAAG,CAACsC,EAAE,CAAE,QAAQ,EAAE,SAAS,EAAEP,GAAG,CAAE;;MAEnD;MACAwD,IAAI,GAAGA,IAAI,CAAChD,OAAO,CAAE,OAAO,EAAER,GAAG,CAAE;MACnCwD,IAAI,GAAGA,IAAI,CAAChD,OAAO,CAAE,SAAS,EAAE,EAAE,CAAE;MACpCgD,IAAI,GAAGA,IAAI,CAAChD,OAAO,CAAE,cAAc,EAAEF,UAAU,CAAE;;MAEjD;MACA,IAAI,CAACmD,UAAU,CAAE;QAChBD,IAAI,EAAEA,IAAI;QACVrF,IAAI,EAAE;MACP,CAAC,CAAE;MAEH,OAAO,KAAK;IACb,CAAC;IAEDuF,UAAU,EAAE,SAAAA,CAAWjB,CAAC,EAAEO,GAAG,EAAG;MAC/B;MACA,IAAK,CAAE,IAAI,CAACO,WAAW,EAAE,EAAG;QAC3B,OAAO,KAAK;MACb;;MAEA;MACA,IAAI3E,OAAO,GAAG,IAAI;MAClB,IAAKoE,GAAG,CAACW,QAAQ,CAAE,UAAU,CAAE,EAAG;QACjC/E,OAAO,GAAGoE,GAAG,CAACY,OAAO,CAAE,SAAS,CAAE;QAClChF,OAAO,CAACsB,QAAQ,CAAE,QAAQ,CAAE;MAC7B;;MAEA;MACA,IAAI2D,KAAK,GAAG,IAAIC,KAAK,CAAE;QACtBC,MAAM,EAAEf,GAAG;QACXgB,aAAa,EAAE,KAAK;QACpBR,IAAI,EAAE,IAAI,CAACnE,YAAY,EAAE;QACzB4D,OAAO,EAAE,IAAI;QACbgB,OAAO,EAAE,SAAAA,CAAWxB,CAAC,EAAEO,GAAG,EAAG;UAC5B;UACA,IAAKA,GAAG,CAACW,QAAQ,CAAE,UAAU,CAAE,EAAG;YACjC;UACD;;UAEA;UACA,IAAI,CAACO,GAAG,CAAE;YACTC,MAAM,EAAEnB,GAAG,CAACtD,IAAI,CAAE,QAAQ,CAAE;YAC5B0E,MAAM,EAAExF;UACT,CAAC,CAAE;QACJ,CAAC;QACDyF,MAAM,EAAE,SAAAA,CAAA,EAAY;UACnB,IAAKzF,OAAO,EAAG;YACdA,OAAO,CAACkE,WAAW,CAAE,QAAQ,CAAE;UAChC;QACD;MACD,CAAC,CAAE;;MAEH;MACAe,KAAK,CAACrB,EAAE,CAAE,OAAO,EAAE,eAAe,EAAE,WAAW,CAAE;IAClD,CAAC;IAED0B,GAAG,EAAE,SAAAA,CAAWI,IAAI,EAAG;MACtB;MACAA,IAAI,GAAGrG,GAAG,CAACsG,SAAS,CAAED,IAAI,EAAE;QAC3BH,MAAM,EAAE,EAAE;QACVC,MAAM,EAAE;MACT,CAAC,CAAE;;MAEH;MACA,IAAK,CAAE,IAAI,CAACpD,QAAQ,EAAE,EAAG;QACxB,OAAO,KAAK;MACb;;MAEA;MACA,IAAIgC,GAAG,GAAG/E,GAAG,CAACuG,SAAS,CAAE;QACxBT,MAAM,EAAE,IAAI,CAAC/E,MAAM,CAAEsF,IAAI,CAACH,MAAM,CAAE;QAClC1D,MAAM,EAAE,IAAI,CAACgE,KAAK,CAAE,UAAWzB,GAAG,EAAE0B,IAAI,EAAG;UAC1C;UACA,IAAKJ,IAAI,CAACF,MAAM,EAAG;YAClBE,IAAI,CAACF,MAAM,CAACA,MAAM,CAAEM,IAAI,CAAE;UAC3B,CAAC,MAAM;YACN,IAAI,CAAChG,YAAY,EAAE,CAAC+B,MAAM,CAAEiE,IAAI,CAAE;UACnC;;UAEA;UACAzG,GAAG,CAAC0G,MAAM,CAAED,IAAI,EAAE,IAAI,CAAC7B,GAAG,CAAE;;UAE5B;UACA,IAAI,CAAChB,MAAM,EAAE;QACd,CAAC;MACF,CAAC,CAAE;;MAEH;MACA,IAAI,CAACE,MAAM,EAAE,CAACC,OAAO,CAAE,QAAQ,CAAE;MAEjC,OAAOgB,GAAG;IACX,CAAC;IAED4B,gBAAgB,EAAE,SAAAA,CAAWnC,CAAC,EAAEO,GAAG,EAAG;MACrC;MACA,IAAK,CAAE,IAAI,CAACO,WAAW,EAAE,EAAG;QAC3B,OAAO,KAAK;MACb;;MAEA;MACA,IAAI3E,OAAO,GAAGoE,GAAG,CAACY,OAAO,CAAE,SAAS,CAAE;MACtC,IAAI,CAACiB,eAAe,CAAEjG,OAAO,CAAE;IAChC,CAAC;IAEDiG,eAAe,EAAE,SAAAA,CAAWjG,OAAO,EAAG;MACrC;MACA,IAAK,CAAE,IAAI,CAACoC,QAAQ,EAAE,EAAG;QACxB,OAAO,KAAK;MACb;MAEA,IAAI8D,QAAQ,GAAG,IAAI,CAAChE,GAAG,CAAE,KAAK,CAAE;;MAEhC;MACA,IAAIkC,GAAG,GAAG/E,GAAG,CAACuG,SAAS,CAAE;QACxBT,MAAM,EAAEnF,OAAO;QAEf;QACAmG,MAAM,EAAE,SAAAA,CAAW9F,IAAI,EAAE+F,KAAK,EAAEC,MAAM,EAAEzE,OAAO,EAAG;UACjD;UACA,IAAKvB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,EAAG;YACtC,OAAO+F,KAAK,CAACxE,OAAO,CACnBsE,QAAQ,GAAG,GAAG,GAAGG,MAAM,EACvBH,QAAQ,GAAG,GAAG,GAAGtE,OAAO,CACxB;;YAED;UACD,CAAC,MAAM;YACN,OAAOwE,KAAK,CAACxE,OAAO,CACnBsE,QAAQ,GAAG,IAAI,GAAGG,MAAM,EACxBH,QAAQ,GAAG,IAAI,GAAGtE,OAAO,CACzB;UACF;QACD,CAAC;QACD4D,MAAM,EAAE,SAAAA,CAAWpB,GAAG,EAAG;UACxB/E,GAAG,CAACqF,QAAQ,CAAE,SAAS,EAAEN,GAAG,CAAE;QAC/B,CAAC;QACDkC,KAAK,EAAE,SAAAA,CAAWlC,GAAG,EAAE0B,IAAI,EAAG;UAC7BzG,GAAG,CAACqF,QAAQ,CAAE,SAAS,EAAEN,GAAG,CAAE;QAC/B;MACD,CAAC,CAAE;;MAEH;MACA,IAAI,CAACjB,MAAM,EAAE,CAACC,OAAO,CAAE,QAAQ,CAAE;;MAEjC;MACA,IAAI,CAACH,MAAM,EAAE;;MAEb;MACA5D,GAAG,CAACkH,cAAc,CAAEnC,GAAG,CAAE;;MAEzB;MACA,OAAOA,GAAG;IACX,CAAC;IAEDoC,cAAc,EAAE,SAAAA,CAAA,EAAY;MAC3B;MACA,IAAK,IAAI,CAACxE,WAAW,EAAE,EAAG;QACzB,OAAO,IAAI;MACZ;MAEA,IAAIb,GAAG,GAAG,IAAI,CAACe,GAAG,CAAE,KAAK,CAAE;MAC3B,IAAI0C,IAAI,GAAGvF,GAAG,CAACoC,EAAE,CAChB,yDAAyD,CACzD;MACD,IAAIC,UAAU,GAAGrC,GAAG,CAACsC,EAAE,CAAE,QAAQ,EAAE,SAAS,EAAER,GAAG,CAAE;;MAEnD;MACAyD,IAAI,GAAGA,IAAI,CAAChD,OAAO,CAAE,OAAO,EAAET,GAAG,CAAE;MACnCyD,IAAI,GAAGA,IAAI,CAAChD,OAAO,CAAE,SAAS,EAAE,EAAE,CAAE;MACpCgD,IAAI,GAAGA,IAAI,CAAChD,OAAO,CAAE,cAAc,EAAEF,UAAU,CAAE;;MAEjD;MACA,IAAI,CAACmD,UAAU,CAAE;QAChBD,IAAI,EAAEA,IAAI;QACVrF,IAAI,EAAE;MACP,CAAC,CAAE;MAEH,OAAO,KAAK;IACb,CAAC;IAEDkH,aAAa,EAAE,SAAAA,CAAW5C,CAAC,EAAEO,GAAG,EAAG;MAClC,IAAIpE,OAAO,GAAGoE,GAAG,CAACY,OAAO,CAAE,SAAS,CAAE;;MAEtC;MACA,IAAKnB,CAAC,CAAC6C,QAAQ,EAAG;QACjB,OAAO,IAAI,CAACC,YAAY,CAAE3G,OAAO,CAAE;MACpC;;MAEA;MACAA,OAAO,CAACsB,QAAQ,CAAE,QAAQ,CAAE;;MAE5B;MACA,IAAIsF,OAAO,GAAGvH,GAAG,CAACwH,UAAU,CAAE;QAC7BC,aAAa,EAAE,IAAI;QACnB3B,MAAM,EAAEf,GAAG;QACXC,OAAO,EAAE,IAAI;QACbgB,OAAO,EAAE,SAAAA,CAAA,EAAY;UACpB,IAAI,CAACsB,YAAY,CAAE3G,OAAO,CAAE;QAC7B,CAAC;QACDyF,MAAM,EAAE,SAAAA,CAAA,EAAY;UACnBzF,OAAO,CAACkE,WAAW,CAAE,QAAQ,CAAE;QAChC;MACD,CAAC,CAAE;IACJ,CAAC;IAEDyC,YAAY,EAAE,SAAAA,CAAW3G,OAAO,EAAG;MAClC;MACA,IAAIuC,IAAI,GAAG,IAAI;MAEf,IAAIwE,SAAS,GAAG,IAAI,CAAChF,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC;;MAE7C;MACA1C,GAAG,CAAC2H,MAAM,CAAE;QACX7B,MAAM,EAAEnF,OAAO;QACf+G,SAAS,EAAEA,SAAS;QACpBE,QAAQ,EAAE,SAAAA,CAAA,EAAY;UACrB;UACA1E,IAAI,CAACY,MAAM,EAAE,CAACC,OAAO,CAAE,QAAQ,CAAE;;UAEjC;UACAb,IAAI,CAACU,MAAM,EAAE;QACd;MACD,CAAC,CAAE;IACJ,CAAC;IAEDiE,eAAe,EAAE,SAAAA,CAAWrD,CAAC,EAAEO,GAAG,EAAG;MACpC,IAAIpE,OAAO,GAAGoE,GAAG,CAACY,OAAO,CAAE,SAAS,CAAE;;MAEtC;MACA,IAAK,IAAI,CAACmC,cAAc,CAAEnH,OAAO,CAAE,EAAG;QACrC,IAAI,CAACoH,UAAU,CAAEpH,OAAO,CAAE;MAC3B,CAAC,MAAM;QACN,IAAI,CAACqH,WAAW,CAAErH,OAAO,CAAE;MAC5B;IACD,CAAC;IAEDmH,cAAc,EAAE,SAAAA,CAAWnH,OAAO,EAAG;MACpC,OAAOA,OAAO,CAAC+E,QAAQ,CAAE,YAAY,CAAE;IACxC,CAAC;IAEDqC,UAAU,EAAE,SAAAA,CAAWpH,OAAO,EAAG;MAChCA,OAAO,CAACkE,WAAW,CAAE,YAAY,CAAE;MACnC7E,GAAG,CAACqF,QAAQ,CAAE,MAAM,EAAE1E,OAAO,EAAE,UAAU,CAAE;IAC5C,CAAC;IAEDqH,WAAW,EAAE,SAAAA,CAAWrH,OAAO,EAAG;MACjCA,OAAO,CAACsB,QAAQ,CAAE,YAAY,CAAE;MAChCjC,GAAG,CAACqF,QAAQ,CAAE,MAAM,EAAE1E,OAAO,EAAE,UAAU,CAAE;;MAE3C;MACA;MACA,IAAI,CAACsH,YAAY,CAAEtH,OAAO,CAAE;IAC7B,CAAC;IAEDsH,YAAY,EAAE,SAAAA,CAAWtH,OAAO,EAAG;MAClC,IAAImD,MAAM,GAAGnD,OAAO,CAACuH,QAAQ,CAAE,OAAO,CAAE;MACxC,IAAIC,MAAM,GAAGrE,MAAM,CAACsE,IAAI,CAAE,MAAM,CAAE,CAAC7F,OAAO,CAAE,iBAAiB,EAAE,EAAE,CAAE;;MAEnE;MACA,IAAI8F,QAAQ,GAAG;QACdC,MAAM,EAAE,0CAA0C;QAClDC,SAAS,EAAE,IAAI,CAAC1F,GAAG,CAAE,KAAK,CAAE;QAC5BuB,CAAC,EAAEzD,OAAO,CAACC,KAAK,EAAE;QAClBsF,MAAM,EAAEvF,OAAO,CAACc,IAAI,CAAE,QAAQ,CAAE;QAChCsF,KAAK,EAAE/G,GAAG,CAACwI,SAAS,CAAE7H,OAAO,EAAEwH,MAAM;MACtC,CAAC;;MAED;MACArI,CAAC,CAAC2I,IAAI,CAAE;QACPC,GAAG,EAAE1I,GAAG,CAAC6C,GAAG,CAAE,SAAS,CAAE;QACzBpB,IAAI,EAAEzB,GAAG,CAAC2I,cAAc,CAAEN,QAAQ,CAAE;QACpCO,QAAQ,EAAE,MAAM;QAChB1I,IAAI,EAAE,MAAM;QACZ2I,OAAO,EAAE,SAAAA,CAAWxH,IAAI,EAAG;UAC1B,IAAKA,IAAI,EAAG;YACXV,OAAO,CACLuH,QAAQ,CAAE,uBAAuB,CAAE,CACnC7G,IAAI,CAAEA,IAAI,CAAE;UACf;QACD;MACD,CAAC,CAAE;IACJ,CAAC;IAEDyH,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,IAAI7E,OAAO,GAAG,EAAE;;MAEhB;MACA,IAAI,CAACvD,QAAQ,EAAE,CAACkB,IAAI,CAAE,UAAWwC,CAAC,EAAG;QACpC,IAAKtE,CAAC,CAAE,IAAI,CAAE,CAAC4F,QAAQ,CAAE,YAAY,CAAE,EAAG;UACzCzB,OAAO,CAAC8E,IAAI,CAAE3E,CAAC,CAAE;QAClB;MACD,CAAC,CAAE;;MAEH;MACAH,OAAO,GAAGA,OAAO,CAACvC,MAAM,GAAGuC,OAAO,GAAG,IAAI;;MAEzC;MACAC,UAAU,CAAC8E,IAAI,CAAE,IAAI,CAACnG,GAAG,CAAE,KAAK,CAAE,EAAEoB,OAAO,CAAE;IAC9C,CAAC;IAEDQ,cAAc,EAAE,SAAAA,CAAWD,CAAC,EAAE7D,OAAO,EAAG;MACvC;MACA,IAAK,IAAI,CAACmH,cAAc,CAAEnH,OAAO,CAAE,EAAG;QACrC,IAAI,CAACoH,UAAU,CAAEpH,OAAO,CAAE;MAC3B;IACD,CAAC;IAEDsI,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB;MACA,IAAI,CAAChG,WAAW,CAAE,IAAI,CAAE;;MAExB;MACA,IAAI,CAACiG,GAAG,CAAE,WAAW,CAAE;IACxB;EACD,CAAC,CAAE;EAEHlJ,GAAG,CAACmJ,iBAAiB,CAAEpJ,KAAK,CAAE;;EAE9B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAI8F,KAAK,GAAG7F,GAAG,CAACoJ,MAAM,CAACC,cAAc,CAACpJ,MAAM,CAAE;IAC7CG,MAAM,EAAE;MACP,qBAAqB,EAAE,WAAW;MAClC,6BAA6B,EAAE;IAChC,CAAC;IAEDwD,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB;MACA,IAAI,CAACvC,IAAI,CAAE,IAAI,CAACwB,GAAG,CAAE,MAAM,CAAE,CAAE;;MAE/B;MACA,IAAI,CAACkC,GAAG,CAAC9C,QAAQ,CAAE,cAAc,CAAE;IACpC;EACD,CAAC,CAAE;;EAEH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC;EACAjC,GAAG,CAACsJ,6BAA6B,CAAE,UAAU,EAAE,kBAAkB,CAAE;EACnEtJ,GAAG,CAACsJ,6BAA6B,CAAE,YAAY,EAAE,kBAAkB,CAAE;EACrEtJ,GAAG,CAACsJ,6BAA6B,CAAE,UAAU,EAAE,kBAAkB,CAAE;EACnEtJ,GAAG,CAACsJ,6BAA6B,CAAE,aAAa,EAAE,kBAAkB,CAAE;;EAEtE;EACA,IAAIpF,UAAU,GAAG,IAAIlE,GAAG,CAACuJ,KAAK,CAAE;IAC/BvI,IAAI,EAAE,uBAAuB;IAE7BwI,GAAG,EAAE,SAAAA,CAAWA,GAAG,EAAExE,OAAO,EAAG;MAC9B,IAAIhD,KAAK,GAAG,IAAI,CAACa,GAAG,CAAE2G,GAAG,GAAGxE,OAAO,CAAE,IAAI,CAAC;;MAE1C;MACAhD,KAAK,EAAE;MACP,IAAI,CAACyH,GAAG,CAAED,GAAG,GAAGxE,OAAO,EAAEhD,KAAK,EAAE,IAAI,CAAE;;MAEtC;MACA,IAAKA,KAAK,GAAG,CAAC,EAAG;QAChBwH,GAAG,IAAI,GAAG,GAAGxH,KAAK;MACnB;MAEA,OAAOwH,GAAG;IACX,CAAC;IAEDrF,IAAI,EAAE,SAAAA,CAAWqF,GAAG,EAAG;MACtB,IAAIA,GAAG,GAAG,IAAI,CAACA,GAAG,CAAEA,GAAG,EAAE,MAAM,CAAE;MACjC,IAAI/H,IAAI,GAAGzB,GAAG,CAAC0J,aAAa,CAAE,IAAI,CAAC1I,IAAI,CAAE;MAEzC,IAAKS,IAAI,IAAIA,IAAI,CAAE+H,GAAG,CAAE,EAAG;QAC1B,OAAO/H,IAAI,CAAE+H,GAAG,CAAE;MACnB,CAAC,MAAM;QACN,OAAO,KAAK;MACb;IACD,CAAC;IAEDR,IAAI,EAAE,SAAAA,CAAWQ,GAAG,EAAEzC,KAAK,EAAG;MAC7B,IAAIyC,GAAG,GAAG,IAAI,CAACA,GAAG,CAAEA,GAAG,EAAE,MAAM,CAAE;MACjC,IAAI/H,IAAI,GAAGzB,GAAG,CAAC0J,aAAa,CAAE,IAAI,CAAC1I,IAAI,CAAE,IAAI,CAAC,CAAC;;MAE/C;MACA,IAAK+F,KAAK,KAAK,IAAI,EAAG;QACrB,OAAOtF,IAAI,CAAE+H,GAAG,CAAE;;QAElB;MACD,CAAC,MAAM;QACN/H,IAAI,CAAE+H,GAAG,CAAE,GAAGzC,KAAK;MACpB;;MAEA;MACA,IAAKjH,CAAC,CAAC6J,aAAa,CAAElI,IAAI,CAAE,EAAG;QAC9BA,IAAI,GAAG,IAAI;MACZ;;MAEA;MACAzB,GAAG,CAAC4J,aAAa,CAAE,IAAI,CAAC5I,IAAI,EAAES,IAAI,CAAE;IACrC;EACD,CAAC,CAAE;AACJ,CAAC,EAAIoI,MAAM,CAAE;;;;;;;;;;ACrpBb,CAAE,UAAW/J,CAAC,EAAG;EAChB,IAAIC,KAAK,GAAGC,GAAG,CAACD,KAAK,CAACE,MAAM,CAAE;IAC7BC,IAAI,EAAE,SAAS;IAEfE,MAAM,EAAE;MACP,wBAAwB,EAAE,YAAY;MACtC,yBAAyB,EAAE,aAAa;MACxC,2BAA2B,EAAE,eAAe;MAC5C,+BAA+B,EAAE,eAAe;MAChD,0BAA0B,EAAE,cAAc;MAC1C,0BAA0B,EAAE,cAAc;MAC1C,2BAA2B,EAAE,UAAU;MACvCG,SAAS,EAAE,SAAS;MACpBF,SAAS,EAAE;IACZ,CAAC;IAEDyJ,OAAO,EAAE;MACRC,gBAAgB,EAAE,mBAAmB;MACrCC,kBAAkB,EAAE,qBAAqB;MACzCC,MAAM,EAAE;IACT,CAAC;IAEDC,iBAAiB,EAAE,SAAAA,CAAA,EAAY;MAC9BlK,GAAG,CAAC2E,OAAO,CAAE,IAAI,CAACwF,SAAS,EAAE,EAAE,IAAI,CAACvF,GAAG,CAAE;IAC1C,CAAC;IAEDwF,mBAAmB,EAAE,SAAAA,CAAA,EAAY;MAChCpK,GAAG,CAAC0G,MAAM,CAAE,IAAI,CAACyD,SAAS,EAAE,EAAE,IAAI,CAACvF,GAAG,CAAE;IACzC,CAAC;IAEDpE,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,OAAO,IAAI,CAACV,CAAC,CAAE,cAAc,CAAE;IAChC,CAAC;IAEDuK,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB,OAAO,IAAI,CAACvK,CAAC,CAAE,0BAA0B,CAAE;IAC5C,CAAC;IAEDwK,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB,OAAO,IAAI,CAACxK,CAAC,CAAE,yBAAyB,CAAE;IAC3C,CAAC;IAEDyK,WAAW,EAAE,SAAAA,CAAWC,EAAE,EAAG;MAC5B,OAAO,IAAI,CAAC1K,CAAC,CAAE,mCAAmC,GAAG0K,EAAE,GAAG,IAAI,CAAE;IACjE,CAAC;IAEDC,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB,OAAO,IAAI,CAAC3K,CAAC,CAAE,gCAAgC,CAAE;IAClD,CAAC;IAED4K,KAAK,EAAE,SAAAA,CAAA,EAAY;MAClB,OAAO,IAAI,CAAC5K,CAAC,CAAE,mBAAmB,CAAE;IACrC,CAAC;IAED6K,KAAK,EAAE,SAAAA,CAAA,EAAY;MAClB,OAAO,IAAI,CAAC7K,CAAC,CAAE,mBAAmB,CAAE;IACrC,CAAC;IAEDqK,SAAS,EAAE,SAAAA,CAAA,EAAY;MACtB,OAAO,IAAI,CAACrK,CAAC,CAAE,wBAAwB,CAAE;IAC1C,CAAC;IAEDkD,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,IAAIjB,GAAG,GAAGa,QAAQ,CAAE,IAAI,CAACC,GAAG,CAAE,KAAK,CAAE,CAAE;MACvC,IAAIb,KAAK,GAAG,IAAI,CAACsI,YAAY,EAAE,CAAC5I,MAAM;MACtC,OAAOK,GAAG,IAAIC,KAAK,IAAID,GAAG;IAC3B,CAAC;IAEDW,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB;MACA,IAAII,GAAG,GAAG,EAAE;;MAEZ;MACA,IAAI,CAACwH,YAAY,EAAE,CAAC1I,IAAI,CAAE,YAAY;QACrCkB,GAAG,CAACiG,IAAI,CAAEjJ,CAAC,CAAE,IAAI,CAAE,CAAC2B,IAAI,CAAE,IAAI,CAAE,CAAE;MACnC,CAAC,CAAE;;MAEH;MACA,OAAOqB,GAAG,CAACpB,MAAM,GAAGoB,GAAG,GAAG,KAAK;IAChC,CAAC;IAEDwB,iBAAiB,EAAE,SAAAA,CAAWpB,IAAI,EAAG;MACpC;MACA,IAAI,CAACqB,EAAE,CAAE,QAAQ,EAAE,mBAAmB,EAAE,UAAWC,CAAC,EAAG;QACtDtB,IAAI,CAAC0H,QAAQ,CAAEpG,CAAC,EAAE1E,CAAC,CAAE,IAAI,CAAE,CAAE;MAC9B,CAAC,CAAE;IACJ,CAAC;IAEDmD,WAAW,EAAE,SAAAA,CAAWC,IAAI,EAAG;MAC9B;MACA,IAAI,CAACmH,WAAW,EAAE,CAAClH,QAAQ,CAAE;QAC5BC,KAAK,EAAE,yBAAyB;QAChCE,eAAe,EAAE,IAAI;QACrBC,oBAAoB,EAAE,IAAI;QAC1BC,MAAM,EAAE,IAAI;QACZqH,KAAK,EAAE,SAAAA,CAAWnH,KAAK,EAAEC,EAAE,EAAG;UAC7BA,EAAE,CAACmH,WAAW,CAACzJ,IAAI,CAAEsC,EAAE,CAACoH,IAAI,CAAC1J,IAAI,EAAE,CAAE;UACrCsC,EAAE,CAACmH,WAAW,CAACE,UAAU,CAAE,OAAO,CAAE;QACrC,CAAC;QACDnH,MAAM,EAAE,SAAAA,CAAWH,KAAK,EAAEC,EAAE,EAAG;UAC9BT,IAAI,CAACY,MAAM,EAAE,CAACC,OAAO,CAAE,QAAQ,CAAE;QAClC;MACD,CAAC,CAAE;;MAEH;MACA,IAAI,CAACvD,QAAQ,EAAE,CAACyK,SAAS,CAAE;QAC1BC,OAAO,EAAE,GAAG;QACZC,SAAS,EAAE,GAAG;QACd1H,IAAI,EAAE,SAAAA,CAAWC,KAAK,EAAEC,EAAE,EAAG;UAC5B3D,GAAG,CAACoL,mBAAmB,CAAE,gBAAgB,EAAEzH,EAAE,CAAC0H,IAAI,CAACC,MAAM,CAAE;QAC5D;MACD,CAAC,CAAE;IACJ,CAAC;IAED5G,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB;MACA,IAAI,CAACJ,iBAAiB,CAAE,IAAI,CAAE;;MAE9B;MACA,IAAI,CAACV,MAAM,EAAE;IACd,CAAC;IAEDA,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB;MACA,IAAI2H,KAAK,GAAG,IAAI,CAACzL,CAAC,CAAE,mBAAmB,CAAE;MACzC,IAAI0L,IAAI,GAAG,IAAI,CAAC1L,CAAC,CAAE,kBAAkB,CAAE;MACvC,IAAIkC,KAAK,GAAG,IAAI,CAACsI,YAAY,EAAE,CAAC5I,MAAM;;MAEtC;MACA,IAAK,IAAI,CAACsB,MAAM,EAAE,EAAG;QACpBwI,IAAI,CAACvJ,QAAQ,CAAE,UAAU,CAAE;MAC5B,CAAC,MAAM;QACNuJ,IAAI,CAAC3G,WAAW,CAAE,UAAU,CAAE;MAC/B;;MAEA;MACA,IAAK,CAAE7C,KAAK,EAAG;QACduJ,KAAK,CAACtJ,QAAQ,CAAE,UAAU,CAAE;MAC7B,CAAC,MAAM;QACNsJ,KAAK,CAAC1G,WAAW,CAAE,UAAU,CAAE;MAChC;;MAEA;MACA,IAAI,CAACoF,MAAM,EAAE;IACd,CAAC;IAEDA,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB;MACA,IAAIwB,KAAK,GAAG,IAAI,CAACjL,QAAQ,EAAE,CAACiL,KAAK,EAAE;MACnC,IAAI3F,MAAM,GAAG,GAAG;MAChB,IAAI4F,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAEH,KAAK,GAAG3F,MAAM,CAAE;;MAE1C;MACA4F,OAAO,GAAGC,IAAI,CAAC7J,GAAG,CAAE4J,OAAO,EAAE,CAAC,CAAE;;MAEhC;MACA,IAAI,CAAClL,QAAQ,EAAE,CAAC4H,IAAI,CAAE,cAAc,EAAEsD,OAAO,CAAE;IAChD,CAAC;IAEDG,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,IAAI,CAAC5B,MAAM,EAAE;IACd,CAAC;IAED6B,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB;MACA,IAAI,CAACtL,QAAQ,EAAE,CAACyB,QAAQ,CAAE,OAAO,CAAE;;MAEnC;MACA;MACA;;MAEA;MACA,IAAIwJ,KAAK,GAAG,IAAI,CAACjL,QAAQ,EAAE,CAACiL,KAAK,EAAE,GAAG,CAAC;MACvCA,KAAK,GAAG7I,QAAQ,CAAE6I,KAAK,CAAE;MACzBA,KAAK,GAAGE,IAAI,CAAC5J,GAAG,CAAE0J,KAAK,EAAE,GAAG,CAAE;;MAE9B;MACA,IAAI,CAAC3L,CAAC,CAAE,yBAAyB,CAAE,CAACiM,GAAG,CAAE;QAAEN,KAAK,EAAEA,KAAK,GAAG;MAAE,CAAC,CAAE;MAC/D,IAAI,CAACd,KAAK,EAAE,CAACqB,OAAO,CAAE;QAAEP,KAAK,EAAEA,KAAK,GAAG;MAAE,CAAC,EAAE,GAAG,CAAE;MACjD,IAAI,CAACf,KAAK,EAAE,CAACsB,OAAO,CAAE;QAAEC,KAAK,EAAER;MAAM,CAAC,EAAE,GAAG,CAAE;IAC9C,CAAC;IAEDS,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB;MACA,IAAI,CAAC1L,QAAQ,EAAE,CAACqE,WAAW,CAAE,OAAO,CAAE;;MAEtC;MACA,IAAI,CAAC4F,OAAO,EAAE,CAAC5F,WAAW,CAAE,QAAQ,CAAE;;MAEtC;MACA7E,GAAG,CAAC2E,OAAO,CAAE,IAAI,CAACgG,KAAK,EAAE,CAAE;;MAE3B;MACA,IAAIR,SAAS,GAAG,IAAI,CAACrK,CAAC,CAAE,wBAAwB,CAAE;MAClD,IAAI,CAAC4K,KAAK,EAAE,CAACsB,OAAO,CAAE;QAAEC,KAAK,EAAE;MAAE,CAAC,EAAE,GAAG,CAAE;MACzC,IAAI,CAACtB,KAAK,EAAE,CAACqB,OAAO,CAAE;QAAEP,KAAK,EAAE;MAAE,CAAC,EAAE,GAAG,EAAE,YAAY;QACpDtB,SAAS,CAAC9I,IAAI,CAAE,EAAE,CAAE;MACrB,CAAC,CAAE;IACJ,CAAC;IAEDoE,UAAU,EAAE,SAAAA,CAAWjB,CAAC,EAAEO,GAAG,EAAG;MAC/B;MACA,IAAK,IAAI,CAAC/B,MAAM,EAAE,EAAG;QACpB,IAAI,CAACwC,UAAU,CAAE;UAChBD,IAAI,EAAEvF,GAAG,CAACoC,EAAE,CAAE,2BAA2B,CAAE;UAC3ClC,IAAI,EAAE;QACP,CAAC,CAAE;QACH;MACD;;MAEA;MACA,IAAIiM,KAAK,GAAGnM,GAAG,CAACoM,aAAa,CAAE;QAC9BC,IAAI,EAAE,QAAQ;QACdlK,KAAK,EAAEnC,GAAG,CAACoC,EAAE,CAAE,sBAAsB,CAAE;QACvCkK,KAAK,EAAE,IAAI,CAACzJ,GAAG,CAAE,KAAK,CAAE;QACxB0J,QAAQ,EAAE,KAAK;QACfC,OAAO,EAAE,IAAI,CAAC3J,GAAG,CAAE,SAAS,CAAE;QAC9B4J,YAAY,EAAE,IAAI,CAAC5J,GAAG,CAAE,YAAY,CAAE;QACtC6J,QAAQ,EAAE,IAAI,CAAC5J,GAAG,EAAE;QACpB6J,MAAM,EAAE7M,CAAC,CAAC0G,KAAK,CAAE,UAAWoG,UAAU,EAAExI,CAAC,EAAG;UAC3C,IAAI,CAACyI,gBAAgB,CAAED,UAAU,EAAExI,CAAC,CAAE;QACvC,CAAC,EAAE,IAAI;MACR,CAAC,CAAE;IACJ,CAAC;IAEDyI,gBAAgB,EAAE,SAAAA,CAAWD,UAAU,EAAExI,CAAC,EAAG;MAC5C;MACAwI,UAAU,GAAG,IAAI,CAACE,kBAAkB,CAAEF,UAAU,CAAE;;MAElD;MACA,IAAK,IAAI,CAAC5J,MAAM,EAAE,EAAG;QACpB;MACD;;MAEA;MACA,IAAK,IAAI,CAACuH,WAAW,CAAEqC,UAAU,CAACpC,EAAE,CAAE,CAAC9I,MAAM,EAAG;QAC/C;MACD;;MAEA;MACA,IAAIL,IAAI,GAAG,CACV,+CAA+C,GAC9CuL,UAAU,CAACpC,EAAE,GACb,IAAI,EACL,8BAA8B,GAC7BoC,UAAU,CAACpC,EAAE,GACb,UAAU,GACV,IAAI,CAACuC,YAAY,EAAE,GACnB,MAAM,EACP,+BAA+B,EAC/B,yBAAyB,EACzB,qBAAqB,EACrB,QAAQ,EACR,8BAA8B,EAC9B,QAAQ,EACR,uBAAuB,EACvB,wEAAwE,GACvEH,UAAU,CAACpC,EAAE,GACb,QAAQ,EACT,QAAQ,EACR,QAAQ,CACR,CAACwC,IAAI,CAAE,EAAE,CAAE;MACZ,IAAI1L,KAAK,GAAGxB,CAAC,CAAEuB,IAAI,CAAE;;MAErB;MACA,IAAI,CAACgJ,WAAW,EAAE,CAAC7H,MAAM,CAAElB,KAAK,CAAE;;MAElC;MACA,IAAK,IAAI,CAACuB,GAAG,CAAE,QAAQ,CAAE,KAAK,SAAS,EAAG;QACzC,IAAIoK,OAAO,GAAG,IAAI,CAAC3C,YAAY,EAAE,CAAC4C,EAAE,CAAE9I,CAAC,CAAE;QACzC,IAAK6I,OAAO,CAACvL,MAAM,EAAG;UACrBuL,OAAO,CAAC9G,MAAM,CAAE7E,KAAK,CAAE;QACxB;MACD;;MAEA;MACA,IAAI,CAAC6L,gBAAgB,CAAEP,UAAU,CAAE;;MAEnC;MACA,IAAI,CAAChJ,MAAM,EAAE;;MAEb;MACA,IAAI,CAACE,MAAM,EAAE,CAACC,OAAO,CAAE,QAAQ,CAAE;IAClC,CAAC;IAED+I,kBAAkB,EAAE,SAAAA,CAAWF,UAAU,EAAG;MAC3C;MACAA,UAAU,GAAG5M,GAAG,CAACsG,SAAS,CAAEsG,UAAU,EAAE;QACvCpC,EAAE,EAAE,EAAE;QACN9B,GAAG,EAAE,EAAE;QACP0E,GAAG,EAAE,EAAE;QACPjL,KAAK,EAAE,EAAE;QACTkL,QAAQ,EAAE,EAAE;QACZnN,IAAI,EAAE;MACP,CAAC,CAAE;;MAEH;MACA,IAAK0M,UAAU,CAACU,UAAU,EAAG;QAC5BV,UAAU,GAAGA,UAAU,CAACU,UAAU;;QAElC;QACA,IAAI5E,GAAG,GAAG1I,GAAG,CAACuN,KAAK,CAClBX,UAAU,EACV,OAAO,EACP,IAAI,CAAC/J,GAAG,CAAE,cAAc,CAAE,EAC1B,KAAK,CACL;QACD,IAAK6F,GAAG,KAAK,IAAI,EAAG;UACnBkE,UAAU,CAAClE,GAAG,GAAGA,GAAG;QACrB;MACD;;MAEA;MACA,OAAOkE,UAAU;IAClB,CAAC;IAEDO,gBAAgB,EAAE,SAAAA,CAAWP,UAAU,EAAG;MACzC;MACAA,UAAU,GAAG,IAAI,CAACE,kBAAkB,CAAEF,UAAU,CAAE;;MAElD;MACA,IAAI7H,GAAG,GAAG,IAAI,CAACwF,WAAW,CAAEqC,UAAU,CAACpC,EAAE,CAAE;;MAE3C;MACA,IAAKoC,UAAU,CAAC1M,IAAI,IAAI,OAAO,EAAG;QACjC;QACA6E,GAAG,CAACpD,IAAI,CAAE,WAAW,CAAE,CAACgG,MAAM,EAAE;;QAEhC;MACD,CAAC,MAAM;QACN;QACA,IAAI6F,KAAK,GAAGxN,GAAG,CAACuN,KAAK,CAAEX,UAAU,EAAE,OAAO,EAAE,KAAK,CAAE;QACnD,IAAKY,KAAK,KAAK,IAAI,EAAG;UACrBZ,UAAU,CAAClE,GAAG,GAAG8E,KAAK;QACvB;;QAEA;QACAzI,GAAG,CAACpD,IAAI,CAAE,WAAW,CAAE,CAAC4D,IAAI,CAAEqH,UAAU,CAACS,QAAQ,CAAE;MACpD;;MAEA;MACA,IAAK,CAAET,UAAU,CAAClE,GAAG,EAAG;QACvBkE,UAAU,CAAClE,GAAG,GAAG1I,GAAG,CAAC6C,GAAG,CAAE,cAAc,CAAE;QAC1CkC,GAAG,CAAC9C,QAAQ,CAAE,OAAO,CAAE;MACxB;;MAEA;MACA8C,GAAG,CAACpD,IAAI,CAAE,KAAK,CAAE,CAACyG,IAAI,CAAE;QACvBqF,GAAG,EAAEb,UAAU,CAAClE,GAAG;QACnB0E,GAAG,EAAER,UAAU,CAACQ,GAAG;QACnBjL,KAAK,EAAEyK,UAAU,CAACzK;MACnB,CAAC,CAAE;;MAEH;MACAnC,GAAG,CAAC8C,GAAG,CAAEiC,GAAG,CAACpD,IAAI,CAAE,OAAO,CAAE,EAAEiL,UAAU,CAACpC,EAAE,CAAE;IAC9C,CAAC;IAEDkD,cAAc,EAAE,SAAAA,CAAWlD,EAAE,EAAG;MAC/B;MACA,IAAI2B,KAAK,GAAGnM,GAAG,CAACoM,aAAa,CAAE;QAC9BC,IAAI,EAAE,MAAM;QACZlK,KAAK,EAAEnC,GAAG,CAACoC,EAAE,CAAE,YAAY,CAAE;QAC7BuL,MAAM,EAAE3N,GAAG,CAACoC,EAAE,CAAE,cAAc,CAAE;QAChCwK,UAAU,EAAEpC,EAAE;QACd8B,KAAK,EAAE,IAAI,CAACzJ,GAAG,CAAE,KAAK,CAAE;QACxB8J,MAAM,EAAE7M,CAAC,CAAC0G,KAAK,CAAE,UAAWoG,UAAU,EAAExI,CAAC,EAAG;UAC3C,IAAI,CAAC+I,gBAAgB,CAAEP,UAAU,CAAE;UACnC;QACD,CAAC,EAAE,IAAI;MACR,CAAC,CAAE;IACJ,CAAC;IAEDgB,WAAW,EAAE,SAAAA,CAAWpJ,CAAC,EAAEO,GAAG,EAAG;MAChC,IAAIyF,EAAE,GAAGzF,GAAG,CAACtD,IAAI,CAAE,IAAI,CAAE;MACzB,IAAK+I,EAAE,EAAG;QACT,IAAI,CAACkD,cAAc,CAAElD,EAAE,CAAE;MAC1B;IACD,CAAC;IAEDqD,gBAAgB,EAAE,SAAAA,CAAWrD,EAAE,EAAG;MACjC;MACA,IAAI,CAAC0B,YAAY,EAAE;;MAEnB;MACA,IAAI,CAAC3B,WAAW,CAAEC,EAAE,CAAE,CAAC7C,MAAM,EAAE;;MAE/B;MACA,IAAI,CAAC/D,MAAM,EAAE;;MAEb;MACA,IAAI,CAACE,MAAM,EAAE,CAACC,OAAO,CAAE,QAAQ,CAAE;IAClC,CAAC;IAEDqD,aAAa,EAAE,SAAAA,CAAW5C,CAAC,EAAEO,GAAG,EAAG;MAClC;MACAP,CAAC,CAACsJ,cAAc,EAAE;MAClBtJ,CAAC,CAACuJ,eAAe,EAAE;;MAEnB;MACA,IAAIvD,EAAE,GAAGzF,GAAG,CAACtD,IAAI,CAAE,IAAI,CAAE;MACzB,IAAK+I,EAAE,EAAG;QACT,IAAI,CAACqD,gBAAgB,CAAErD,EAAE,CAAE;MAC5B;IACD,CAAC;IAEDwD,gBAAgB,EAAE,SAAAA,CAAWxD,EAAE,EAAG;MACjC;MACA,IAAIzF,GAAG,GAAG,IAAI,CAACwF,WAAW,CAAEC,EAAE,CAAE;;MAEhC;MACA,IAAKzF,GAAG,CAACW,QAAQ,CAAE,QAAQ,CAAE,EAAG;QAC/B;MACD;;MAEA;MACA,IAAIuI,KAAK,GAAG,IAAI,CAACzH,KAAK,CAAE,YAAY;QACnC;QACA,IAAI,CAACmE,KAAK,EAAE,CAAChJ,IAAI,CAAE,QAAQ,CAAE,CAACoC,OAAO,CAAE,MAAM,CAAE;;QAE/C;QACA,IAAI,CAAC0G,OAAO,EAAE,CAAC5F,WAAW,CAAE,QAAQ,CAAE;;QAEtC;QACAE,GAAG,CAAC9C,QAAQ,CAAE,QAAQ,CAAE;;QAExB;QACA,IAAI,CAAC6J,WAAW,EAAE;;QAElB;QACAoC,KAAK,EAAE;MACR,CAAC,CAAE;;MAEH;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC1H,KAAK,CAAE,YAAY;QACnC;QACA,IAAI6B,QAAQ,GAAG;UACdC,MAAM,EAAE,mCAAmC;UAC3CC,SAAS,EAAE,IAAI,CAAC1F,GAAG,CAAE,KAAK,CAAE;UAC5B2H,EAAE,EAAEA;QACL,CAAC;;QAED;QACA,IAAK,IAAI,CAAC2D,GAAG,CAAE,KAAK,CAAE,EAAG;UACxB,IAAI,CAACtL,GAAG,CAAE,KAAK,CAAE,CAACuL,KAAK,EAAE;QAC1B;;QAEA;QACApO,GAAG,CAACqO,WAAW,CAAE,IAAI,CAAClE,SAAS,EAAE,CAAE;;QAEnC;QACA,IAAImE,GAAG,GAAGxO,CAAC,CAAC2I,IAAI,CAAE;UACjBC,GAAG,EAAE1I,GAAG,CAAC6C,GAAG,CAAE,SAAS,CAAE;UACzBpB,IAAI,EAAEzB,GAAG,CAAC2I,cAAc,CAAEN,QAAQ,CAAE;UACpCnI,IAAI,EAAE,MAAM;UACZ0I,QAAQ,EAAE,MAAM;UAChB2F,KAAK,EAAE,KAAK;UACZ1F,OAAO,EAAE2F;QACV,CAAC,CAAE;;QAEH;QACA,IAAI,CAAC/E,GAAG,CAAE,KAAK,EAAE6E,GAAG,CAAE;MACvB,CAAC,CAAE;;MAEH;MACA,IAAIE,KAAK,GAAG,IAAI,CAAChI,KAAK,CAAE,UAAWnF,IAAI,EAAG;QACzC;QACA,IAAK,CAAEA,IAAI,EAAG;UACb;QACD;;QAEA;QACA,IAAIsJ,KAAK,GAAG,IAAI,CAACR,SAAS,EAAE;;QAE5B;QACAQ,KAAK,CAACtJ,IAAI,CAAEA,IAAI,CAAE;;QAElB;QACAsJ,KAAK,CAAChJ,IAAI,CAAE,6BAA6B,CAAE,CAACgG,MAAM,EAAE;;QAEpD;QACAgD,KAAK,CACHhJ,IAAI,CAAE,4BAA4B,CAAE,CACpCa,MAAM,CACNmI,KAAK,CAAChJ,IAAI,CAAE,0CAA0C,CAAE,CACxD;;QAEF;QACA3B,GAAG,CAACqF,QAAQ,CAAE,QAAQ,EAAEsF,KAAK,CAAE;MAChC,CAAC,CAAE;;MAEH;MACAsD,KAAK,EAAE;IACR,CAAC;IAEDQ,aAAa,EAAE,SAAAA,CAAWjK,CAAC,EAAEO,GAAG,EAAG;MAClC,IAAIyF,EAAE,GAAGzF,GAAG,CAACtD,IAAI,CAAE,IAAI,CAAE;MACzB,IAAK+I,EAAE,EAAG;QACT,IAAI,CAACwD,gBAAgB,CAAExD,EAAE,CAAE;MAC5B;IACD,CAAC;IAEDkE,YAAY,EAAE,SAAAA,CAAWlK,CAAC,EAAEO,GAAG,EAAG;MACjC,IAAI,CAACmH,YAAY,EAAE;IACpB,CAAC;IAEDyC,YAAY,EAAE,SAAAA,CAAWnK,CAAC,EAAEO,GAAG,EAAG;MACjC;MACA,IAAKA,GAAG,CAACW,QAAQ,CAAE,UAAU,CAAE,EAAG;QACjC;MACD;;MAEA;MACA,IAAI5C,GAAG,GAAGiC,GAAG,CAACjC,GAAG,EAAE;MACnB,IAAK,CAAEA,GAAG,EAAG;QACZ;MACD;;MAEA;MACA,IAAI8L,GAAG,GAAG,EAAE;MACZ,IAAI,CAACtE,YAAY,EAAE,CAAC1I,IAAI,CAAE,YAAY;QACrCgN,GAAG,CAAC7F,IAAI,CAAEjJ,CAAC,CAAE,IAAI,CAAE,CAAC2B,IAAI,CAAE,IAAI,CAAE,CAAE;MACnC,CAAC,CAAE;;MAEH;MACA,IAAIwM,KAAK,GAAG,IAAI,CAACzH,KAAK,CAAE,YAAY;QACnC;QACA,IAAI6B,QAAQ,GAAG;UACdC,MAAM,EAAE,mCAAmC;UAC3CC,SAAS,EAAE,IAAI,CAAC1F,GAAG,CAAE,KAAK,CAAE;UAC5B+L,GAAG,EAAEA,GAAG;UACRC,IAAI,EAAE/L;QACP,CAAC;;QAED;QACA,IAAIwL,GAAG,GAAGxO,CAAC,CAAC2I,IAAI,CAAE;UACjBC,GAAG,EAAE1I,GAAG,CAAC6C,GAAG,CAAE,SAAS,CAAE;UACzB+F,QAAQ,EAAE,MAAM;UAChB1I,IAAI,EAAE,MAAM;UACZqO,KAAK,EAAE,KAAK;UACZ9M,IAAI,EAAEzB,GAAG,CAAC2I,cAAc,CAAEN,QAAQ,CAAE;UACpCQ,OAAO,EAAEqF;QACV,CAAC,CAAE;MACJ,CAAC,CAAE;;MAEH;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC1H,KAAK,CAAE,UAAWsI,IAAI,EAAG;QACzC;QACA,IAAK,CAAE9O,GAAG,CAAC+O,aAAa,CAAED,IAAI,CAAE,EAAG;UAClC;QACD;;QAEA;QACAA,IAAI,CAACrN,IAAI,CAACuN,OAAO,EAAE;;QAEnB;QACAF,IAAI,CAACrN,IAAI,CAACwN,GAAG,CAAE,UAAWzE,EAAE,EAAG;UAC9B,IAAI,CAACH,WAAW,EAAE,CAAC6E,OAAO,CAAE,IAAI,CAAC3E,WAAW,CAAEC,EAAE,CAAE,CAAE;QACrD,CAAC,EAAE,IAAI,CAAE;MACV,CAAC,CAAE;;MAEH;MACAyD,KAAK,EAAE;IACR,CAAC;IAEDrD,QAAQ,EAAE,SAAAA,CAAWpG,CAAC,EAAEO,GAAG,EAAG;MAC7B;MACA,IAAIoK,OAAO,GAAG,IAAI,CAACrP,CAAC,CAAE,qBAAqB,CAAE;;MAE7C;MACA,IAAKqP,OAAO,CAACzJ,QAAQ,CAAE,UAAU,CAAE,EAAG;QACrC;MACD;;MAEA;MACA,IAAI2C,QAAQ,GAAGrI,GAAG,CAACwI,SAAS,CAAE,IAAI,CAAC2B,SAAS,EAAE,CAAE;;MAEhD;MACAgF,OAAO,CAAClN,QAAQ,CAAE,UAAU,CAAE;MAC9BkN,OAAO,CAAChJ,MAAM,CAAE,8BAA8B,CAAE;;MAEhD;MACAkC,QAAQ,CAACC,MAAM,GAAG,sCAAsC;;MAExD;MACAxI,CAAC,CAAC2I,IAAI,CAAE;QACPC,GAAG,EAAE1I,GAAG,CAAC6C,GAAG,CAAE,SAAS,CAAE;QACzBpB,IAAI,EAAEzB,GAAG,CAAC2I,cAAc,CAAEN,QAAQ,CAAE;QACpCnI,IAAI,EAAE,MAAM;QACZ0I,QAAQ,EAAE,MAAM;QAChBhB,QAAQ,EAAE,SAAAA,CAAA,EAAY;UACrBuH,OAAO,CAACtK,WAAW,CAAE,UAAU,CAAE;UACjCsK,OAAO,CAACC,IAAI,CAAE,cAAc,CAAE,CAACzH,MAAM,EAAE;QACxC;MACD,CAAC,CAAE;IACJ,CAAC;IAEDsB,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB;MACA,IAAI,CAAChG,WAAW,CAAE,IAAI,CAAE;;MAExB;MACA,IAAI,CAACiG,GAAG,CAAE,WAAW,CAAE;IACxB;EACD,CAAC,CAAE;EAEHlJ,GAAG,CAACmJ,iBAAiB,CAAEpJ,KAAK,CAAE;;EAE9B;EACAC,GAAG,CAACsJ,6BAA6B,CAAE,UAAU,EAAE,SAAS,CAAE;EAC1DtJ,GAAG,CAACsJ,6BAA6B,CAAE,YAAY,EAAE,SAAS,CAAE;EAC5DtJ,GAAG,CAACsJ,6BAA6B,CAAE,mBAAmB,EAAE,SAAS,CAAE;EACnEtJ,GAAG,CAACsJ,6BAA6B,CAAE,sBAAsB,EAAE,SAAS,CAAE;AACvE,CAAC,EAAIO,MAAM,CAAE;;;;;;;;;;ACpmBb,CAAE,UAAW/J,CAAC,EAAG;EAChB,IAAIC,KAAK,GAAGC,GAAG,CAACD,KAAK,CAACE,MAAM,CAAE;IAC7BC,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,EAAE;IACRkP,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE,CAAC;IAEblP,MAAM,EAAE;MACP,+BAA+B,EAAE,YAAY;MAC7C,qCAAqC,EAAE,kBAAkB;MACzD,kCAAkC,EAAE,eAAe;MACnD,oCAAoC,EAAE,iBAAiB;MACvD,iDAAiD,EAAG,kBAAkB;MACtE,gDAAgD,EAAG,iBAAiB;MACpE,gDAAgD,EAAE,iBAAiB;MACnE,gDAAgD,EAAE,iBAAiB;MACnE,sBAAsB,EAAE,qBAAqB;MAC7C,6BAA6B,EAAE,iBAAiB;MAChD,uBAAuB,EAAE,gBAAgB;MACzC,yBAAyB,EAAE,kBAAkB;MAC7C,oBAAoB,EAAE,mBAAmB;MACzCC,SAAS,EAAE,QAAQ;MACnBC,WAAW,EAAE,UAAU;MACvBC,SAAS,EAAE,SAAS;MACpBgP,MAAM,EAAE;IACT,CAAC;IAED/O,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,OAAO,IAAI,CAACV,CAAC,CAAE,qBAAqB,CAAE;IACvC,CAAC;IAED0P,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,OAAO,IAAI,CAAC1P,CAAC,CAAE,aAAa,CAAE;IAC/B,CAAC;IAED2P,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,OAAO,IAAI,CAAC3P,CAAC,CAAE,aAAa,CAAE;IAC/B,CAAC;IAED4P,KAAK,EAAE,SAAAA,CAAA,EAAY;MAClB,OAAO,IAAI,CAAC5P,CAAC,CAAE,kBAAkB,CAAE,CAAC6P,GAAG,CAAE,0BAA0B,CAAE;IACtE,CAAC;IAEDC,IAAI,EAAE,SAAAA,CAAWhP,KAAK,EAAG;MACxB,OAAO,IAAI,CAACd,CAAC,CAAE,sBAAsB,GAAGc,KAAK,GAAG,GAAG,CAAE;IACtD,CAAC;IAEDG,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,OAAO,IAAI,CAACjB,CAAC,CAAE,4BAA4B,CAAE;IAC9C,CAAC;IAEDmB,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,OAAO,IAAI,CAACnB,CAAC,CAAE,mBAAmB,CAAE;IACrC,CAAC;IAEDoB,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB,OAAO,IAAI,CAACpB,CAAC,CAAE,2BAA2B,CAAE;IAC7C,CAAC;IAED+P,gBAAgB,EAAE,SAAAA,CAAA,EAAW;MAC5B,OAAO,IAAI,CAAC/P,CAAC,CAAE,gCAAgC,CAAE;IAClD,CAAC;IAEDgQ,eAAe,EAAE,SAAAA,CAAA,EAAW;MAC3B,OAAO,IAAI,CAAChQ,CAAC,CAAE,+BAA+B,CAAE;IACjD,CAAC;IAEDiQ,eAAe,EAAE,SAAAA,CAAA,EAAW;MAC3B,OAAO,IAAI,CAACjQ,CAAC,CAAE,+BAA+B,CAAE;IACjD,CAAC;IAEDkQ,eAAe,EAAE,SAAAA,CAAA,EAAW;MAC3B,OAAO,IAAI,CAAClQ,CAAC,CAAE,+BAA+B,CAAE;IACjD,CAAC;IAEDmQ,UAAU,EAAE,SAAAA,CAAA,EAAW;MACtB,OAAO,IAAI,CAACnQ,CAAC,CAAE,oBAAoB,CAAE;IACtC,CAAC;IAEDoQ,UAAU,EAAE,SAAAA,CAAA,EAAW;MACtB,MAAMA,UAAU,GAAG,IAAI,CAACpQ,CAAC,CAAE,uBAAuB,CAAE,CAACyF,IAAI,EAAE;MAC3D,OAAO3C,QAAQ,CAAEsN,UAAU,CAAE;IAC9B,CAAC;IAEDxN,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,OAAO,IAAI,CAACgN,KAAK,EAAE,CAAChO,MAAM;IAC3B,CAAC;IAEDiB,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB,IAAIwN,OAAO,GAAG,IAAI,CAACrN,GAAG,EAAE;MACxB,IAAIsN,OAAO,GAAGxN,QAAQ,CAAE,IAAI,CAACC,GAAG,CAAE,KAAK,CAAE,CAAE;MAE3C,IAAK,IAAI,CAACA,GAAG,CAAE,YAAY,CAAE,EAAG;QAC/BsN,OAAO,GAAG,IAAI,CAACtN,GAAG,CAAE,YAAY,CAAE;MACnC;MAEA,OAAO,CAAEuN,OAAO,IAAIA,OAAO,GAAGD,OAAO;IACtC,CAAC;IAEDpN,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,IAAIoN,OAAO,GAAG,IAAI,CAACrN,GAAG,EAAE;MACxB,IAAIuN,OAAO,GAAGzN,QAAQ,CAAE,IAAI,CAACC,GAAG,CAAE,KAAK,CAAE,CAAE;MAE3C,IAAK,IAAI,CAACA,GAAG,CAAE,YAAY,CAAE,EAAG;QAC/BsN,OAAO,GAAG,IAAI,CAACtN,GAAG,CAAE,YAAY,CAAE;MACnC;MAEA,OAAO,CAAEwN,OAAO,IAAIA,OAAO,GAAGF,OAAO;IACtC,CAAC;IAEDlN,WAAW,EAAE,SAAAA,CAAWC,IAAI,EAAG;MAC9B;MACA,IAAK,IAAI,CAACL,GAAG,CAAE,KAAK,CAAE,IAAI,CAAC,EAAG;QAC7B;MACD;;MAEA;MACA,IAAK,IAAI,CAACA,GAAG,CAAE,YAAY,CAAC,EAAG;QAC9B;MACD;;MAEA;MACA,IAAI,CAAC4M,MAAM,EAAE,CAACtM,QAAQ,CAAE;QACvBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,YAAY;QACpBC,eAAe,EAAE,IAAI;QACrBC,oBAAoB,EAAE,IAAI;QAC1BC,MAAM,EAAE,IAAI;QACZC,IAAI,EAAE,SAAAA,CAAWC,KAAK,EAAEC,EAAE,EAAG;UAC5BT,IAAI,CAACU,MAAM,EAAE;QACd,CAAC;QACDC,MAAM,EAAE,SAAAA,CAAWH,KAAK,EAAEC,EAAE,EAAG;UAC9BT,IAAI,CAACY,MAAM,EAAE,CAACC,OAAO,CAAE,QAAQ,CAAE;QAClC;MACD,CAAC,CAAE;IACJ,CAAC;IAEDC,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB;MACA,IAAIC,OAAO,GAAGC,UAAU,CAACC,IAAI,CAAE,IAAI,CAACtB,GAAG,CAAE,KAAK,CAAE,CAAE;;MAElD;MACA,IAAK,CAAEoB,OAAO,EAAG;QAChB,OAAO,KAAK;MACb;;MAEA;MACA,IAAI,CAACyL,KAAK,EAAE,CAAC9N,IAAI,CAAE,UAAWwC,CAAC,EAAG;QACjC,IAAKH,OAAO,CAACI,OAAO,CAAED,CAAC,CAAE,GAAG,CAAC,CAAC,EAAG;UAChC,IAAKtE,CAAC,CAAE,IAAI,CAAE,CAAC6B,IAAI,CAAE,oBAAoB,CAAE,CAACD,MAAM,EAAG;YACpD5B,CAAC,CAAE,IAAI,CAAE,CAACmC,QAAQ,CAAE,YAAY,CAAE;UACnC;QACD;MACD,CAAC,CAAE;IACJ,CAAC;IAEDqC,iBAAiB,EAAE,SAAAA,CAAWpB,IAAI,EAAG;MACpC;MACA,IAAI,CAACqB,EAAE,CAAE,cAAc,EAAE,UAAU,EAAE,UAAWC,CAAC,EAAG;QACnD,IAAIoL,IAAI,GAAG9P,CAAC,CAAE,IAAI,CAAE;QACpB,IAAKoD,IAAI,CAACoN,WAAW,CAAEV,IAAI,CAAE,EAAG;UAC/B1M,IAAI,CAACqN,MAAM,CAAEX,IAAI,CAAE;QACpB;MACD,CAAC,CAAE;;MAEH;MACA,IAAK,IAAI,CAAC/M,GAAG,CAAE,YAAY,CAAE,EAAG;QAC/B,IAAI,CAAC0B,EAAE,CAAE,QAAQ,EAAE,yBAAyB,EAAE,UAAWC,CAAC,EAAG;UAC5D,MAAMgM,QAAQ,GAAG1Q,CAAC,CAAE0E,CAAC,CAACiM,aAAa,CAAE;UACrC,IAAK,CAAED,QAAQ,CAAC9K,QAAQ,CAAE,iBAAiB,CAAE,IAAI,CAAE8K,QAAQ,CAAC9K,QAAQ,CAAE,gBAAgB,CAAE,EAAG;YAC1FxC,IAAI,CAACwN,aAAa,CAAElM,CAAC,EAAE1E,CAAC,CAAE,IAAI,CAAE,CAAE;UACnC;QACD,CAAC,CAAE;MACJ;MAEA,IAAI,CAAC6Q,uBAAuB,EAAE;IAC/B,CAAC;IAEDjM,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB;MACA,IAAI,CAACJ,iBAAiB,CAAE,IAAI,CAAE;;MAE9B;MACA,IAAI,CAACN,YAAY,EAAE;;MAEnB;MACAhE,GAAG,CAAC2E,OAAO,CAAE,IAAI,CAAC5D,MAAM,EAAE,EAAE,IAAI,CAAC6D,GAAG,CAAE;;MAEtC;MACA,IAAK,IAAI,CAAC/B,GAAG,CAAE,YAAY,CAAE,EAAG;QAC/B,IAAI,CAACyM,UAAU,GAAG,IAAI,CAACzM,GAAG,CAAE,YAAY,CAAE;MAC3C;;MAEA;MACA,IAAI,CAACe,MAAM,EAAE;IACd,CAAC;IAEDA,MAAM,EAAE,SAAAA,CAAA,EAAyC;MAAA,IAA9BgN,oBAAoB,GAAAC,SAAA,CAAAnP,MAAA,QAAAmP,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;MAC7C;MACA,IAAKD,oBAAoB,EAAG;QAC3B,IAAI,CAAClB,KAAK,EAAE,CAAC9N,IAAI,CAAE,UAAWwC,CAAC,EAAG;UACjCtE,CAAC,CAAE,IAAI,CAAE,CACP6B,IAAI,CAAE,iBAAiB,CAAE,CACzBN,IAAI,CAAE+C,CAAC,GAAG,CAAC,CAAE;QAChB,CAAC,CAAE;MACJ;;MAEA;MACA,IAAI5D,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;MAC9B,IAAIU,OAAO,GAAG,IAAI,CAACA,OAAO,EAAE;;MAE5B;MACA,IAAK,IAAI,CAAC4B,GAAG,EAAE,IAAI,CAAC,EAAG;QACtBtC,QAAQ,CAACyB,QAAQ,CAAE,QAAQ,CAAE;MAC9B,CAAC,MAAM;QACNzB,QAAQ,CAACqE,WAAW,CAAE,QAAQ,CAAE;MACjC;;MAEA;MACA,IAAK,CAAE,IAAI,CAAC9B,QAAQ,EAAE,EAAG;QACxBvC,QAAQ,CAACyB,QAAQ,CAAE,MAAM,CAAE;QAC3Bf,OAAO,CAACe,QAAQ,CAAE,UAAU,CAAE;MAC/B,CAAC,MAAM;QACNzB,QAAQ,CAACqE,WAAW,CAAE,MAAM,CAAE;QAC9B3D,OAAO,CAAC2D,WAAW,CAAE,UAAU,CAAE;MAClC;MAEA,IAAK,IAAI,CAAChC,GAAG,CAAE,YAAY,CAAE,EAAG;QAC/B,IAAI,CAACkO,sBAAsB,EAAE;MAC9B;;MAEA;MACA;MACA;MACA;MACA;MACA;IACD,CAAC;;IAEDJ,uBAAuB,EAAE,SAAAA,CAAA,EAAW;MACnC,IAAK,CAAE3Q,GAAG,CAACgR,WAAW,EAAE,IAAI,CAAE,IAAI,CAACnO,GAAG,CAAE,YAAY,CAAE,EAAG;QACxD;MACD;MAEA,IAAIoO,gBAAgB,GAAG,IAAI;MAC3BC,EAAE,CAACzP,IAAI,CAAC0P,SAAS,CAAE,MAAM;QACxB,IAAKD,EAAE,CAACzP,IAAI,CAACkL,MAAM,CAAE,gBAAgB,CAAE,CAACyE,iBAAiB,EAAE,EAAG;UAC7DH,gBAAgB,GAAG,KAAK;QACzB,CAAC,MAAM;UACN,IAAK,CAAEA,gBAAgB,EAAG;YACzBA,gBAAgB,GAAG,IAAI;YACvB,IAAI,CAACxH,GAAG,CAAE,YAAY,EAAE,CAAC,EAAE,IAAI,CAAE;YACjC,IAAI,CAAC4H,YAAY,CAAE,IAAI,CAAE;UAC1B;QACD;MACD,CAAC,CAAE;IACJ,CAAC;IAEDC,kBAAkB,EAAE,SAAAA,CAAA,EAAW;MAC9B,IAAIC,SAAS,GAAG,IAAI,CAAC1O,GAAG,CAAE,YAAY,CAAE;MACxC,IAAI,CAAC4G,GAAG,CAAE,YAAY,EAAE,EAAE8H,SAAS,EAAE,IAAI,CAAE;IAC5C,CAAC;IAEDC,kBAAkB,EAAE,SAAAA,CAAA,EAAW;MAC9B,IAAID,SAAS,GAAG,IAAI,CAAC1O,GAAG,CAAE,YAAY,CAAE;MACxC,IAAI,CAAC4G,GAAG,CAAE,YAAY,EAAE,EAAE8H,SAAS,EAAE,IAAI,CAAE;IAC5C,CAAC;IAEDjM,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB;MACA,IAAK,IAAI,CAACvC,QAAQ,EAAE,EAAG;QACtB,OAAO,IAAI;MACZ;;MAEA;MACA,IAAIhB,GAAG,GAAG,IAAI,CAACc,GAAG,CAAE,KAAK,CAAE;MAC3B,IAAI0C,IAAI,GAAGvF,GAAG,CAACoC,EAAE,CAAE,mCAAmC,CAAE;;MAExD;MACAmD,IAAI,GAAGA,IAAI,CAAChD,OAAO,CAAE,OAAO,EAAER,GAAG,CAAE;;MAEnC;MACA,IAAI,CAACyD,UAAU,CAAE;QAChBD,IAAI,EAAEA,IAAI;QACVrF,IAAI,EAAE;MACP,CAAC,CAAE;;MAEH;MACA,OAAO,KAAK;IACb,CAAC;IAEDuF,UAAU,EAAE,SAAAA,CAAWjB,CAAC,EAAEO,GAAG,EAAG;MAC/B;MACA,IAAK,CAAE,IAAI,CAACO,WAAW,EAAE,EAAG;QAC3B,OAAO,KAAK;MACb;;MAEA;MACA,IAAKP,GAAG,CAACW,QAAQ,CAAE,UAAU,CAAE,EAAG;QACjC,IAAI,CAACO,GAAG,CAAE;UACTE,MAAM,EAAEpB,GAAG,CAACY,OAAO,CAAE,UAAU;QAChC,CAAC,CAAE;;QAEH;MACD,CAAC,MAAM;QACN,IAAI,CAACM,GAAG,EAAE;MACX;IACD,CAAC;IAEDA,GAAG,EAAE,SAAAA,CAAWI,IAAI,EAAG;MACtB;MACA,IAAK,CAAE,IAAI,CAACtD,QAAQ,EAAE,EAAG;QACxB,OAAO,KAAK;MACb;;MAEA;MACAsD,IAAI,GAAGrG,GAAG,CAACsG,SAAS,CAAED,IAAI,EAAE;QAC3BF,MAAM,EAAE;MACT,CAAC,CAAE;;MAEH;MACA,IAAIpB,GAAG,GAAG/E,GAAG,CAACuG,SAAS,CAAE;QACxBT,MAAM,EAAE,IAAI,CAAC/E,MAAM,EAAE;QACrByB,MAAM,EAAE,IAAI,CAACgE,KAAK,CAAE,UAAWzB,GAAG,EAAE0B,IAAI,EAAG;UAC1C;UACA,IAAKJ,IAAI,CAACF,MAAM,EAAG;YAClBE,IAAI,CAACF,MAAM,CAACA,MAAM,CAAEM,IAAI,CAAE;UAC3B,CAAC,MAAM;YACN1B,GAAG,CAACoB,MAAM,CAAEM,IAAI,CAAE;UACnB;;UAEA;UACAA,IAAI,CAAC5B,WAAW,CAAE,WAAW,CAAE;;UAE/B;UACA7E,GAAG,CAAC0G,MAAM,CAAED,IAAI,EAAE,IAAI,CAAC7B,GAAG,CAAE;QAC7B,CAAC;MACF,CAAC,CAAE;MAEH,IAAK,IAAI,CAAC/B,GAAG,CAAE,YAAY,CAAE,EAAG;QAC/B,IAAI,CAACyO,kBAAkB,EAAE;QAEzB,IAAK,KAAK,KAAKjL,IAAI,CAACF,MAAM,EAAG;UAC5B;UACA,MAAMsL,UAAU,GAAG7O,QAAQ,CAAEyD,IAAI,CAACF,MAAM,CAACxE,IAAI,CAAE,iBAAiB,CAAE,CAAC+P,KAAK,EAAE,CAACnM,IAAI,EAAE,CAAE,IAAI,CAAC;UACxF,IAAIoM,SAAS,GAAGF,UAAU;UAE1B,IAAKE,SAAS,IAAI,CAAEtL,IAAI,CAACF,MAAM,CAACT,QAAQ,CAAE,cAAc,CAAE,IAAI,CAAEW,IAAI,CAACF,MAAM,CAACT,QAAQ,CAAE,WAAW,CAAE,EAAG;YACrG,EAAEiM,SAAS;UACZ;UAEA,IAAKtL,IAAI,CAACF,MAAM,CAACT,QAAQ,CAAE,aAAa,CAAE,EAAG;YAC5CW,IAAI,CAACF,MAAM,CAACtB,WAAW,CAAE,aAAa,CAAE;YACxCE,GAAG,CAAC9C,QAAQ,CAAE,aAAa,CAAE;UAC9B;UAEA,IAAI,CAAC2P,eAAe,CAAE7M,GAAG,EAAE,UAAU,CAAE;UACvC,IAAI,CAAC6M,eAAe,CAAE7M,GAAG,EAAE,WAAW,EAAE4M,SAAS,CAAE;;UAEnD;UACA5M,GAAG,CAACpD,IAAI,CAAE,iBAAiB,CAAE,CAAC+P,KAAK,EAAE,CAACG,IAAI,EAAE,CAACtM,IAAI,CAAEoM,SAAS,CAAE;UAC9D,IAAK,CAAE5M,GAAG,CAACpD,IAAI,CAAE,uBAAuB,CAAE,CAAC+D,QAAQ,CAAE,UAAU,CAAE,EAAG;YACnE,IAAIoM,OAAO,GAAI9R,GAAG,CAACoC,EAAE,CAAE,kCAAkC,CAAE;YAC3D2C,GAAG,CAACpD,IAAI,CAAE,uBAAuB,CAAE,CAACM,QAAQ,CAAE,UAAU,CAAE;YAC1D8C,GAAG,CAACpD,IAAI,CAAE,iBAAiB,CAAE,CAAC+P,KAAK,EAAE,CAACzK,KAAK,CAAE,eAAe,GAAG6K,OAAO,GAAG,YAAY,CAAE;UACxF;UACA/M,GAAG,CAACpD,IAAI,CAAE,kBAAkB,CAAE,CAAC+P,KAAK,EAAE,CAACG,IAAI,EAAE;UAC7C9M,GAAG,CAACqD,IAAI,CAAE,eAAe,EAAEuJ,SAAS,CAAE;QACvC,CAAC,MAAM;UACN,IAAI,CAACrC,UAAU,EAAE;UAEjBvK,GAAG,CAACpD,IAAI,CAAE,kBAAkB,CAAE,CAAC+P,KAAK,EAAE,CAAC5O,GAAG,CAAE,IAAI,CAACwM,UAAU,CAAE;UAC7DvK,GAAG,CAACpD,IAAI,CAAE,iBAAiB,CAAE,CAAC+P,KAAK,EAAE,CAACnM,IAAI,CAAE,IAAI,CAAC+J,UAAU,CAAE;UAC7D,IAAI,CAACsC,eAAe,CAAE7M,GAAG,EAAE,OAAO,CAAE;UAEpC,IAAK,CAAE,IAAI,CAAC0K,MAAM,EAAE,CAAC9N,IAAI,CAAE,cAAc,CAAE,CAACD,MAAM,EAAG;YACpDqD,GAAG,CAAC9C,QAAQ,CAAE,aAAa,CAAE;UAC9B;QACD;QAEA8C,GAAG,CAACpD,IAAI,CAAE,kBAAkB,CAAE,CAC5BA,IAAI,CAAE,4CAA4C,CAAE,CACpD+P,KAAK,EAAE,CACP3N,OAAO,CAAE,OAAO,CAAE;MACrB;;MAEA;MACA,IAAI,CAACH,MAAM,EAAE;MACb,IAAI,CAACE,MAAM,EAAE,CAACC,OAAO,CAAE,QAAQ,CAAE;MAEjC,OAAOgB,GAAG;IACX,CAAC;IAED4B,gBAAgB,EAAE,SAAAA,CAAWnC,CAAC,EAAEO,GAAG,EAAG;MACrC;MACA,IAAK,CAAE,IAAI,CAACO,WAAW,EAAE,EAAG;QAC3B,OAAO,KAAK;MACb;;MAEA;MACA,IAAIsK,IAAI,GAAG7K,GAAG,CAACY,OAAO,CAAE,UAAU,CAAE;MACpC,IAAI,CAACoM,YAAY,CAAEnC,IAAI,CAAE;IAC1B,CAAC;IAEDmC,YAAY,EAAE,SAAAA,CAAWnC,IAAI,EAAG;MAC/B;MACA,IAAK,CAAE,IAAI,CAAC7M,QAAQ,EAAE,EAAG;QACxB,OAAO,KAAK;MACb;;MAEA;MACA,IAAI8D,QAAQ,GAAG,IAAI,CAAChE,GAAG,CAAE,KAAK,CAAE;;MAEhC;MACA,IAAIkC,GAAG,GAAG/E,GAAG,CAACuG,SAAS,CAAE;QACxBT,MAAM,EAAE8J,IAAI;QAEZ;QACA9I,MAAM,EAAE,SAAAA,CAAW9F,IAAI,EAAE+F,KAAK,EAAEC,MAAM,EAAEzE,OAAO,EAAG;UACjD;UACA,IAAKvB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,EAAG;YACtC,OAAO+F,KAAK,CAACxE,OAAO,CACnBsE,QAAQ,GAAG,GAAG,GAAGG,MAAM,EACvBH,QAAQ,GAAG,GAAG,GAAGtE,OAAO,CACxB;;YAED;UACD,CAAC,MAAM;YACN,OAAOwE,KAAK,CAACxE,OAAO,CACnBsE,QAAQ,GAAG,IAAI,GAAGG,MAAM,EACxBH,QAAQ,GAAG,IAAI,GAAGtE,OAAO,CACzB;UACF;QACD,CAAC;QACD4D,MAAM,EAAE,SAAAA,CAAWpB,GAAG,EAAG;UACxB/E,GAAG,CAACqF,QAAQ,CAAE,SAAS,EAAEN,GAAG,CAAE;QAC/B,CAAC;QACDkC,KAAK,EAAE,SAAAA,CAAWlC,GAAG,EAAE0B,IAAI,EAAG;UAC7BzG,GAAG,CAACqF,QAAQ,CAAE,SAAS,EAAEN,GAAG,CAAE;QAC/B;MACD,CAAC,CAAE;MAEH,IAAK,IAAI,CAAClC,GAAG,CAAE,YAAY,CAAE,EAAG;QAC/B,IAAI,CAACyO,kBAAkB,EAAE;;QAEzB;QACA,MAAMG,UAAU,GAAG7O,QAAQ,CAAEgN,IAAI,CAACjO,IAAI,CAAE,iBAAiB,CAAE,CAAC+P,KAAK,EAAE,CAACnM,IAAI,EAAE,CAAE,IAAI,CAAC;QAEjF,IAAI,CAACqM,eAAe,CAAE7M,GAAG,EAAE,UAAU,CAAE;QACvC,IAAI,CAAC6M,eAAe,CAAE7M,GAAG,EAAE,WAAW,EAAE0M,UAAU,CAAE;;QAEpD;QACA1M,GAAG,CAACpD,IAAI,CAAE,iBAAiB,CAAE,CAAC+P,KAAK,EAAE,CAACG,IAAI,EAAE;QAC5C,IAAK,CAAE9M,GAAG,CAACpD,IAAI,CAAE,uBAAuB,CAAE,CAAC+D,QAAQ,CAAE,UAAU,CAAE,EAAG;UACnE,IAAIoM,OAAO,GAAI9R,GAAG,CAACoC,EAAE,CAAE,kCAAkC,CAAE;UAC3D2C,GAAG,CAACpD,IAAI,CAAE,uBAAuB,CAAE,CAACM,QAAQ,CAAE,UAAU,CAAE;UAC1D8C,GAAG,CAACpD,IAAI,CAAE,iBAAiB,CAAE,CAAC+P,KAAK,EAAE,CAACzK,KAAK,CAAE,eAAe,GAAG6K,OAAO,GAAG,YAAY,CAAE;QACxF;QACA/M,GAAG,CAACpD,IAAI,CAAE,kBAAkB,CAAE,CAAC+P,KAAK,EAAE,CAACG,IAAI,EAAE;QAC7C9M,GAAG,CAACqD,IAAI,CAAE,eAAe,EAAEqJ,UAAU,CAAE;QACvC1M,GAAG,CAACF,WAAW,CAAE,aAAa,CAAE;MACjC;;MAEA;MACA,IAAI,CAACf,MAAM,EAAE,CAACC,OAAO,CAAE,QAAQ,CAAE;;MAEjC;MACA,IAAI,CAACH,MAAM,EAAE;;MAEb;MACA5D,GAAG,CAACkH,cAAc,CAAEnC,GAAG,CAAE;;MAEzB;MACA,OAAOA,GAAG;IACX,CAAC;IAEDoC,cAAc,EAAE,SAAAA,CAAA,EAAY;MAC3B;MACA,IAAK,IAAI,CAACxE,WAAW,EAAE,EAAG;QACzB,OAAO,IAAI;MACZ;;MAEA;MACA,IAAIb,GAAG,GAAG,IAAI,CAACe,GAAG,CAAE,KAAK,CAAE;MAC3B,IAAI0C,IAAI,GAAGvF,GAAG,CAACoC,EAAE,CAAE,uCAAuC,CAAE;;MAE5D;MACAmD,IAAI,GAAGA,IAAI,CAAChD,OAAO,CAAE,OAAO,EAAET,GAAG,CAAE;;MAEnC;MACA,IAAI,CAAC0D,UAAU,CAAE;QAChBD,IAAI,EAAEA,IAAI;QACVrF,IAAI,EAAE;MACP,CAAC,CAAE;;MAEH;MACA,OAAO,KAAK;IACb,CAAC;IAEDkH,aAAa,EAAE,SAAAA,CAAW5C,CAAC,EAAEO,GAAG,EAAG;MAClC,IAAI6K,IAAI,GAAG7K,GAAG,CAACY,OAAO,CAAE,UAAU,CAAE;;MAEpC;MACA,IAAKnB,CAAC,CAAC6C,QAAQ,EAAG;QACjB,OAAO,IAAI,CAACM,MAAM,CAAEiI,IAAI,CAAE;MAC3B;;MAEA;MACAA,IAAI,CAAC3N,QAAQ,CAAE,QAAQ,CAAE;;MAEzB;MACA,IAAIsF,OAAO,GAAGvH,GAAG,CAACwH,UAAU,CAAE;QAC7BC,aAAa,EAAE,IAAI;QACnB3B,MAAM,EAAEf,GAAG;QACXC,OAAO,EAAE,IAAI;QACbgB,OAAO,EAAE,SAAAA,CAAA,EAAY;UACpB,IAAI,CAAC2B,MAAM,CAAEiI,IAAI,CAAE;QACpB,CAAC;QACDxJ,MAAM,EAAE,SAAAA,CAAA,EAAY;UACnBwJ,IAAI,CAAC/K,WAAW,CAAE,QAAQ,CAAE;QAC7B;MACD,CAAC,CAAE;IACJ,CAAC;IAEDmN,eAAe,EAAE,SAAAA,CAAUxN,CAAC,EAAEO,GAAG,EAAG;MACnC,IAAK,CAAE,IAAI,CAAClC,GAAG,CAAE,YAAY,CAAE,EAAG;QACjC;MACD;MAEA,IAAKkC,GAAG,CAACW,QAAQ,CAAE,UAAU,CAAE,EAAG;QACjC;MACD;MAEAX,GAAG,CAACpD,IAAI,CAAE,iBAAiB,CAAE,CAACkQ,IAAI,EAAE;MACpC9M,GAAG,CAACpD,IAAI,CAAE,kBAAkB,CAAE,CAACsQ,IAAI,EAAE,CAAClO,OAAO,CAAE,QAAQ,CAAE;IAC1D,CAAC;IAEDmO,cAAc,EAAE,SAAAA,CAAU1N,CAAC,EAAEO,GAAG,EAAG;MAClC,IAAI,CAACoN,gBAAgB,CAAE3N,CAAC,EAAEO,GAAG,EAAE,KAAK,CAAE;IACvC,CAAC;IAEDoN,gBAAgB,EAAE,SAAAA,CAAU3N,CAAC,EAAEO,GAAG,EAAkB;MAAA,IAAhBlB,MAAM,GAAAgN,SAAA,CAAAnP,MAAA,QAAAmP,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;MAChD,IAAK,CAAE,IAAI,CAAChO,GAAG,CAAE,YAAY,CAAE,EAAG;QACjC;MACD;MAEA,MAAM+M,IAAI,GAAG7K,GAAG,CAACY,OAAO,CAAE,UAAU,CAAE;MACtC,MAAMyM,UAAU,GAAGxC,IAAI,CAACjO,IAAI,CAAE,iBAAiB,CAAE,CAAC+P,KAAK,EAAE;MACzD,IAAIW,OAAO,GAAGtN,GAAG,CAACjC,GAAG,EAAE;MAEvB8M,IAAI,CAACjO,IAAI,CAAE,kBAAkB,CAAE,CAAC+P,KAAK,EAAE,CAACG,IAAI,EAAE;MAE9C,IAAK,CAAE7R,GAAG,CAACsS,SAAS,CAAED,OAAO,CAAE,IAAIE,UAAU,CAAEF,OAAO,CAAE,GAAG,CAAC,EAAG;QAC9DD,UAAU,CAACH,IAAI,EAAE;QACjB;MACD;MAEAI,OAAO,GAAG1G,IAAI,CAACC,KAAK,CAAEyG,OAAO,CAAE;MAE/B,MAAMG,QAAQ,GAAGH,OAAO,GAAG,CAAC;MAE5BtN,GAAG,CAACjC,GAAG,CAAEuP,OAAO,CAAE;MAClBD,UAAU,CAAC7M,IAAI,CAAE8M,OAAO,CAAE,CAACJ,IAAI,EAAE;MAEjC,IAAKpO,MAAM,EAAG;QACb,IAAI,CAAC+N,eAAe,CAAEhC,IAAI,EAAE,WAAW,EAAE4C,QAAQ,CAAE;MACpD;IACD,CAAC;IAEDC,iBAAiB,EAAE,SAAAA,CAAA,EAAW;MAC7B,MAAMC,OAAO,GAAG9P,QAAQ,CAAE,IAAI,CAACC,GAAG,CAAE,UAAU,CAAE,CAAE,IAAI,EAAE;MACxD,MAAM0O,SAAS,GAAG3O,QAAQ,CAAE,IAAI,CAACC,GAAG,CAAE,YAAY,CAAE,CAAE,IAAI,CAAC;MAC3D,MAAMqN,UAAU,GAAGvE,IAAI,CAACgH,IAAI,CAAEpB,SAAS,GAAGmB,OAAO,CAAE;;MAEnD;MACA,IAAI,CAAC5S,CAAC,CAAE,uBAAuB,CAAE,CAACyF,IAAI,CAAE2K,UAAU,CAAE;MACpD,IAAI,CAACZ,UAAU,GAAGiC,SAAS;;MAE3B;MACA,IAAK,IAAI,CAAClC,IAAI,GAAGa,UAAU,EAAG;QAC7B,IAAI,CAACb,IAAI,GAAGa,UAAU;QACtB,IAAI,CAACmB,YAAY,EAAE;MACpB;IACD,CAAC;IAED1J,MAAM,EAAE,SAAAA,CAAWiI,IAAI,EAAG;MACzB,MAAM1M,IAAI,GAAG,IAAI;MAEjB,IAAK,IAAI,CAACL,GAAG,CAAE,YAAY,CAAE,EAAG;QAC/B,IAAI,CAAC2O,kBAAkB,EAAE;;QAEzB;QACA,IAAK5B,IAAI,CAACnO,IAAI,CAAE,IAAI,CAAE,CAACmR,QAAQ,CAAE,MAAM,CAAE,EAAE;UAC1C,IAAI,CAAChB,eAAe,CAAEhC,IAAI,EAAE,SAAS,CAAE;UAEvCA,IAAI,CAACiC,IAAI,EAAE;UACX3O,IAAI,CAACY,MAAM,EAAE,CAACC,OAAO,CAAE,QAAQ,CAAE;UACjCb,IAAI,CAACU,MAAM,CAAE,KAAK,CAAE;UACpB;QACD,CAAC,MAAM,IAAKgM,IAAI,CAAClK,QAAQ,CAAE,aAAa,CAAE,EAAG;UAC5CkK,IAAI,CAACiD,IAAI,CAAE,YAAY,CAAE,CAAC5Q,QAAQ,CAAE,aAAa,CAAE;QACpD;MACD;;MAEA;MACAjC,GAAG,CAAC2H,MAAM,CAAE;QACX7B,MAAM,EAAE8J,IAAI;QACZlI,SAAS,EAAE,CAAC;QACZE,QAAQ,EAAE,SAAAA,CAAA,EAAY;UACrB;UACA1E,IAAI,CAACY,MAAM,EAAE,CAACC,OAAO,CAAE,QAAQ,CAAE;;UAEjC;UACAb,IAAI,CAACU,MAAM,EAAE;;UAEb;UACA;QACD;MACD,CAAC,CAAE;IACJ,CAAC;;IAED0M,WAAW,EAAE,SAAAA,CAAWV,IAAI,EAAG;MAC9B,OAAOA,IAAI,CAAClK,QAAQ,CAAE,YAAY,CAAE;IACrC,CAAC;IAEDoN,QAAQ,EAAE,SAAAA,CAAWlD,IAAI,EAAG;MAC3BA,IAAI,CAAC3N,QAAQ,CAAE,YAAY,CAAE;MAC7BjC,GAAG,CAACqF,QAAQ,CAAE,MAAM,EAAEuK,IAAI,EAAE,UAAU,CAAE;IACzC,CAAC;IAEDW,MAAM,EAAE,SAAAA,CAAWX,IAAI,EAAG;MACzBA,IAAI,CAAC/K,WAAW,CAAE,YAAY,CAAE;MAChC7E,GAAG,CAACqF,QAAQ,CAAE,MAAM,EAAEuK,IAAI,EAAE,UAAU,CAAE;IACzC,CAAC;IAED/H,eAAe,EAAE,SAAAA,CAAWrD,CAAC,EAAEO,GAAG,EAAG;MACpC;MACA,IAAI6K,IAAI,GAAG7K,GAAG,CAACY,OAAO,CAAE,UAAU,CAAE;MACpC,IAAIoN,WAAW,GAAG,IAAI,CAACzC,WAAW,CAAEV,IAAI,CAAE;;MAE1C;MACA,IAAKpL,CAAC,CAAC6C,QAAQ,EAAG;QACjBuI,IAAI,GAAG,IAAI,CAACF,KAAK,EAAE;MACpB;;MAEA;MACA,IAAKqD,WAAW,EAAG;QAClB,IAAI,CAACxC,MAAM,CAAEX,IAAI,CAAE;MACpB,CAAC,MAAM;QACN,IAAI,CAACkD,QAAQ,CAAElD,IAAI,CAAE;MACtB;IACD,CAAC;IAED9K,MAAM,EAAE,SAAAA,CAAWN,CAAC,EAAEO,GAAG,EAAEC,OAAO,EAAG;MACpC;MACA,IAAIC,MAAM,GAAGjF,GAAG,CAACkF,SAAS,CAAE;QAC3BC,EAAE,EAAE,UAAU;QACdC,MAAM,EAAE,IAAI,CAACL;MACd,CAAC,CAAE;;MAEH;MACA;MACA;MACA/E,GAAG,CAACqF,QAAQ,CAAE,aAAa,EAAEJ,MAAM,CAAE;IACtC,CAAC;IAED6D,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB;MACA,IAAI7E,OAAO,GAAG,EAAE;;MAEhB;MACA,IAAI,CAACyL,KAAK,EAAE,CAAC9N,IAAI,CAAE,UAAWwC,CAAC,EAAG;QACjC,IAAKtE,CAAC,CAAE,IAAI,CAAE,CAAC4F,QAAQ,CAAE,YAAY,CAAE,EAAG;UACzCzB,OAAO,CAAC8E,IAAI,CAAE3E,CAAC,CAAE;QAClB;MACD,CAAC,CAAE;;MAEH;MACAH,OAAO,GAAGA,OAAO,CAACvC,MAAM,GAAGuC,OAAO,GAAG,IAAI;;MAEzC;MACAC,UAAU,CAAC8E,IAAI,CAAE,IAAI,CAACnG,GAAG,CAAE,KAAK,CAAE,EAAEoB,OAAO,CAAE;IAC9C,CAAC;IAEDgF,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB;MACA,IAAI,CAAChG,WAAW,CAAE,IAAI,CAAE;;MAExB;MACA,IAAI,CAACiG,GAAG,CAAE,WAAW,CAAE;IACxB,CAAC;IAEDwH,aAAa,EAAE,SAAAA,CAAUlM,CAAC,EAAEO,GAAG,EAAG;MACjC,MAAMiO,OAAO,GAAGlT,CAAC,CAAE0E,CAAC,CAACyO,cAAc,CAAE;MACrC,IAAIrD,IAAI,GAAG7K,GAAG,CAACY,OAAO,CAAE,UAAU,CAAE;MAEpC,IAAKiK,IAAI,CAACjK,OAAO,CAAE,qBAAqB,CAAE,CAAClE,IAAI,CAAE,KAAK,CAAE,KAAKuR,OAAO,CAACvR,IAAI,CAAE,KAAK,CAAE,EAAG;QACpFmO,IAAI,GAAGA,IAAI,CAACxK,MAAM,EAAE,CAACO,OAAO,CAAE,UAAU,CAAE;MAC3C;MAEA,IAAI,CAACiM,eAAe,CAAEhC,IAAI,EAAE,SAAS,CAAE;IACxC,CAAC;IAEDgC,eAAe,EAAE,SAAAA,CAAUhC,IAAI,EAAEsD,MAAM,EAAgB;MAAA,IAAdzR,IAAI,GAAAoP,SAAA,CAAAnP,MAAA,QAAAmP,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;MACnD,IAAK,CAAE,IAAI,CAAChO,GAAG,CAAE,YAAY,CAAE,EAAG;QACjC;MACD;MAEA,MAAMsQ,UAAU,GAAGvD,IAAI,CAACwD,OAAO,CAAE,qBAAqB,CAAE,CAAC3R,IAAI,CAAE,KAAK,CAAE;MAEtE,IAAK,IAAI,CAAC2D,MAAM,EAAE,IAAI+N,UAAU,KAAK,IAAI,CAACtQ,GAAG,CAAE,KAAK,CAAE,EAAG;QACxD;MACD;MAEA,MAAMwQ,MAAM,GAAGzD,IAAI,CAACnO,IAAI,CAAE,IAAI,CAAE;MAChC,MAAM6R,UAAU,GAAG,IAAI,CAACvO,GAAG,CAACpD,IAAI,CAAE,kCAAkC,CAAE,CAACyG,IAAI,CAAE,MAAM,CAAE;MACrF,MAAMmL,WAAW,GAAI,GAAED,UAAW,IAAGD,MAAO,SAAQH,MAAO,GAAE;MAC7D,MAAMM,YAAY,GAAI,qDAAoDD,WAAY,YAAW9R,IAAK,MAAK;MAE3G,IAAK,CAAEmO,IAAI,CAAClK,QAAQ,CAAE,MAAM,GAAGwN,MAAM,CAAE,EAAG;QACzCtD,IAAI,CAAC3N,QAAQ,CAAE,MAAM,GAAGiR,MAAM,CAAE;MACjC;;MAEA;MACA,MAAMO,gBAAgB,GAAG7D,IAAI,CAACjO,IAAI,CAAG,eAAc4R,WAAY,IAAG,CAAE;MACpE,IAAK,CAAEE,gBAAgB,CAAC/R,MAAM,EAAG;QAChCkO,IAAI,CAACjO,IAAI,CAAE,IAAI,CAAE,CAAC+P,KAAK,EAAE,CAAClP,MAAM,CAAEgR,YAAY,CAAE;MACjD,CAAC,MAAM;QACNC,gBAAgB,CAAC3Q,GAAG,CAAErB,IAAI,CAAE;MAC7B;IACD,CAAC;IAEDiS,gBAAgB,EAAE,SAAAA,CAAA,EAAW;MAC5B,IAAI,CAACC,YAAY,CAAE,CAAC,CAAE;IACvB,CAAC;IAEDC,eAAe,EAAE,SAAAA,CAAA,EAAW;MAC3B,IAAI,CAACD,YAAY,CAAE,IAAI,CAACtE,IAAI,GAAG,CAAC,CAAE;IACnC,CAAC;IAEDwE,eAAe,EAAE,SAAAA,CAAUrP,CAAC,EAAG;MAC9B,IAAI,CAACmP,YAAY,CAAE,IAAI,CAACtE,IAAI,GAAG,CAAC,CAAE;IACnC,CAAC;IAEDyE,eAAe,EAAE,SAAAA,CAAA,EAAW;MAC3B,IAAI,CAACH,YAAY,CAAE,IAAI,CAACzD,UAAU,EAAE,CAAE;IACvC,CAAC;IAED6D,mBAAmB,EAAE,SAAAA,CAAA,EAAW;MAC/B,IAAI,CAACJ,YAAY,CAAE,IAAI,CAAC1D,UAAU,EAAE,CAACnN,GAAG,EAAE,CAAE;IAC7C,CAAC;IAEDiO,sBAAsB,EAAE,SAAAA,CAAA,EAAW;MAClC,IAAI,CAAC9P,QAAQ,EAAE,CAACU,IAAI,CAAE,UAAU,CAAE,CAACkD,WAAW,CAAE,UAAU,CAAE;MAE5D,IAAK,IAAI,CAACwK,IAAI,IAAI,CAAC,EAAG;QACrB,IAAI,CAACQ,gBAAgB,EAAE,CAAC5N,QAAQ,CAAE,UAAU,CAAE;QAC9C,IAAI,CAAC6N,eAAe,EAAE,CAAC7N,QAAQ,CAAE,UAAU,CAAE;MAC9C;MAEA,IAAK,IAAI,CAACoN,IAAI,IAAI,IAAI,CAACa,UAAU,EAAE,EAAG;QACrC,IAAI,CAACH,eAAe,EAAE,CAAC9N,QAAQ,CAAE,UAAU,CAAE;QAC7C,IAAI,CAAC+N,eAAe,EAAE,CAAC/N,QAAQ,CAAE,UAAU,CAAE;MAC9C;IACD,CAAC;IAED0R,YAAY,EAAE,SAAAA,CAAUK,QAAQ,EAAG;MAClC,MAAM9Q,IAAI,GAAG,IAAI;;MAEjB;MACAlD,GAAG,CAACiU,YAAY,CAAE;QACjBC,IAAI,EAAE,IAAI,CAAC1T,QAAQ,EAAE;QACrBkD,KAAK,EAAE,EAAE;QACTyQ,KAAK,EAAE,IAAI;QACXtL,OAAO,EAAE,SAAAA,CAAUuL,KAAK,EAAG;UAC1BlR,IAAI,CAACmM,IAAI,GAAG2E,QAAQ;;UAEpB;UACA,IAAK9Q,IAAI,CAACmM,IAAI,IAAI,CAAC,EAAG;YACrBnM,IAAI,CAACmM,IAAI,GAAG,CAAC;UACd;UACA,IAAKnM,IAAI,CAACmM,IAAI,IAAInM,IAAI,CAACgN,UAAU,EAAE,EAAG;YACrChN,IAAI,CAACmM,IAAI,GAAGnM,IAAI,CAACgN,UAAU,EAAE;UAC9B;UAEAhN,IAAI,CAACmO,YAAY,EAAE;QACpB,CAAC;QACDgD,OAAO,EAAE,SAAAA,CAAUD,KAAK,EAAG;UAC1BlR,IAAI,CAAC+M,UAAU,EAAE,CAACnN,GAAG,CAAEI,IAAI,CAACmM,IAAI,CAAE;UAClC,OAAO,KAAK;QACb;MACD,CAAC,CAAE;IACJ,CAAC;IAEDgC,YAAY,EAAE,SAAAA,CAAA,EAAiC;MAAA,IAAvBiD,YAAY,GAAAzD,SAAA,CAAAnP,MAAA,QAAAmP,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;MAC3C,MAAMxI,QAAQ,GAAGrI,GAAG,CAAC2I,cAAc,CAAE;QACpCL,MAAM,EAAE,yBAAyB;QACjCiM,KAAK,EAAE,IAAI,CAAClF,IAAI;QAChB9G,SAAS,EAAE,IAAI,CAAC1F,GAAG,CAAE,KAAK,CAAE;QAC5B2R,UAAU,EAAE,IAAI,CAAC3R,GAAG,CAAE,WAAW,CAAE;QACnC4R,aAAa,EAAE7R,QAAQ,CAAE,IAAI,CAACC,GAAG,CAAE,UAAU,CAAE,CAAE;QACjD6R,OAAO,EAAEJ;MACV,CAAC,CAAE;MAEHxU,CAAC,CAAC2I,IAAI,CACL;QACCC,GAAG,EAAEiM,OAAO;QACZC,MAAM,EAAE,MAAM;QACdhM,QAAQ,EAAE,MAAM;QAChBnH,IAAI,EAAE4G,QAAQ;QACdrD,OAAO,EAAE;MACV,CAAC,CACD,CAAC6P,IAAI,CACL,UAAUC,QAAQ,EAAG;QACpB,MAAM;UAAEC;QAAK,CAAC,GAAGD,QAAQ,CAACrT,IAAI;QAC9B,MAAMuT,aAAa,GAAG,IAAI,CAACvF,MAAM,EAAE,CAAC9N,IAAI,CAAE,MAAM,CAAE;QAElDqT,aAAa,CAACrF,GAAG,CAAE,YAAY,CAAE,CAACkC,IAAI,EAAE;QAExC,IAAKyC,YAAY,EAAG;UACnB;UACAU,aAAa,CAACrF,GAAG,CAAE,YAAY,CAAE,CAAChI,MAAM,EAAE;;UAE1C;UACA,IAAI,CAAC8B,GAAG,CAAE,YAAY,EAAEqL,QAAQ,CAACrT,IAAI,CAACwT,UAAU,EAAE,KAAK,CAAE;QAC1D,CAAC,MAAM;UACND,aAAa,CAACrF,GAAG,CAAE,mFAAmF,CAAE,CAAChI,MAAM,EAAE;QAClH;QAEAuN,MAAM,CAACC,IAAI,CAAEJ,IAAI,CAAE,CAACK,OAAO,CAAExU,KAAK,IAAI;UACrC,IAAIgP,IAAI,GAAW,KAAK;UACxB,IAAIyF,WAAW,GAAI,IAAI,CAAC5F,MAAM,EAAE,CAAC9N,IAAI,CAAE,kBAAkB,GAAGf,KAAK,GAAG,GAAG,CAAE;UACzE,IAAI0U,YAAY,GAAG,IAAI,CAAC7F,MAAM,EAAE,CAAC9N,IAAI,CAAE,oBAAoB,GAAGf,KAAK,GAAG,GAAG,CAAE;;UAE3E;UACA,IAAK0U,YAAY,CAAC5T,MAAM,EAAG;YAC1B4T,YAAY,CAACC,QAAQ,CAAE,IAAI,CAAC9F,MAAM,EAAE,CAAE,CAACwC,IAAI,EAAE;YAC7CjS,GAAG,CAACqF,QAAQ,CAAE,SAAS,EAAEiQ,YAAY,CAAE;UACxC;;UAEA;UACA,IAAKD,WAAW,CAAC3P,QAAQ,CAAE,aAAa,CAAE,EAAG;YAC5C;UACD;;UAEA;UACA,IAAK2P,WAAW,CAAC3T,MAAM,EAAG;YACzB1B,GAAG,CAACqF,QAAQ,CAAE,SAAS,EAAEgQ,WAAW,CAAE;YACtCA,WAAW,CAACE,QAAQ,CAAE,IAAI,CAAC9F,MAAM,EAAE,CAAE,CAACwC,IAAI,EAAE;YAC5CjS,GAAG,CAACqF,QAAQ,CAAE,SAAS,EAAEgQ,WAAW,CAAE;YACtC;UACD;;UAEA;UACAzF,IAAI,GAAG9P,CAAC,CAAEiV,IAAI,CAAEnU,KAAK,CAAE,CAAE;UACzB,IAAI,CAAC6O,MAAM,EAAE,CAACjN,MAAM,CAAEoN,IAAI,CAAE,CAACqC,IAAI,EAAE;UACnCjS,GAAG,CAACqF,QAAQ,CAAE,SAAS,EAAEuK,IAAI,CAAE;;UAE/B;UACA,IAAI,CAAC7O,MAAM,EAAE,CAACwU,QAAQ,CAAE,IAAI,CAAC9F,MAAM,EAAE,CAAE;QACxC,CAAC,CAAE;QAEH,MAAM+F,UAAU,GAAG,IAAI,CAAC/F,MAAM,EAAE,CAAC9N,IAAI,CAAE,mBAAmB,CAAE;;QAE5D;QACA,IAAK6T,UAAU,CAAC9T,MAAM,EAAG;UACxB,MAAMwB,IAAI,GAAG,IAAI;UAEjBsS,UAAU,CAAC5T,IAAI,CAAE,YAAW;YAC3B,MAAM6T,SAAS,GAAG3V,CAAC,CAAE,IAAI,CAAE;YAC3B2V,SAAS,CAACC,YAAY,CAAExS,IAAI,CAACnC,MAAM,EAAE,CAAE,CAACkR,IAAI,EAAE;YAC9CjS,GAAG,CAACqF,QAAQ,CAAE,SAAS,EAAEoQ,SAAS,CAAE;UACrC,CAAC,CAAE;QACJ;;QAEA;QACA,IAAI,CAACxF,UAAU,EAAE,CAACnN,GAAG,CAAE,IAAI,CAACuM,IAAI,CAAE;QAClC,IAAI,CAAC0B,sBAAsB,EAAE;MAC9B,CAAC,CACD,CAAC4E,IAAI,CACL,UAAUC,KAAK,EAAEC,UAAU,EAAEC,WAAW,EAAG;QAC1C,MAAMC,KAAK,GAAG/V,GAAG,CAACgW,WAAW,CAAEJ,KAAK,CAAE;QACtC,IAAI9D,OAAO,GAAG9R,GAAG,CAACoC,EAAE,CAAE,oBAAoB,CAAE;QAE5C,IAAK,EAAE,KAAK2T,KAAK,EAAG;UACnBjE,OAAO,GAAI,GAAEA,OAAQ,KAAIiE,KAAM,EAAC;QACjC;QAEA,IAAI,CAACvQ,UAAU,CAAE;UAChBD,IAAI,EAAEuM,OAAO;UACb5R,IAAI,EAAE;QACP,CAAC,CAAE;MACJ,CAAC,CACD;IACF;EAED,CAAC,CAAE;EAEHF,GAAG,CAACmJ,iBAAiB,CAAEpJ,KAAK,CAAE;;EAE9B;EACAC,GAAG,CAACsJ,6BAA6B,CAAE,UAAU,EAAE,UAAU,CAAE;EAC3DtJ,GAAG,CAACsJ,6BAA6B,CAAE,YAAY,EAAE,UAAU,CAAE;EAC7DtJ,GAAG,CAACsJ,6BAA6B,CAAE,UAAU,EAAE,UAAU,CAAE;EAC3DtJ,GAAG,CAACsJ,6BAA6B,CAAE,aAAa,EAAE,UAAU,CAAE;;EAE9D;EACA,IAAIpF,UAAU,GAAG,IAAIlE,GAAG,CAACuJ,KAAK,CAAE;IAC/BvI,IAAI,EAAE,oBAAoB;IAE1BwI,GAAG,EAAE,SAAAA,CAAWA,GAAG,EAAExE,OAAO,EAAG;MAC9B;MACA,IAAIhD,KAAK,GAAG,IAAI,CAACa,GAAG,CAAE2G,GAAG,GAAGxE,OAAO,CAAE,IAAI,CAAC;;MAE1C;MACAhD,KAAK,EAAE;MACP,IAAI,CAACyH,GAAG,CAAED,GAAG,GAAGxE,OAAO,EAAEhD,KAAK,EAAE,IAAI,CAAE;;MAEtC;MACA,IAAKA,KAAK,GAAG,CAAC,EAAG;QAChBwH,GAAG,IAAI,GAAG,GAAGxH,KAAK;MACnB;;MAEA;MACA,OAAOwH,GAAG;IACX,CAAC;IAEDrF,IAAI,EAAE,SAAAA,CAAWqF,GAAG,EAAG;MACtB;MACA,IAAIA,GAAG,GAAG,IAAI,CAACA,GAAG,CAAEA,GAAG,EAAE,MAAM,CAAE;MACjC,IAAI/H,IAAI,GAAGzB,GAAG,CAAC0J,aAAa,CAAE,IAAI,CAAC1I,IAAI,CAAE;;MAEzC;MACA,IAAKS,IAAI,IAAIA,IAAI,CAAE+H,GAAG,CAAE,EAAG;QAC1B,OAAO/H,IAAI,CAAE+H,GAAG,CAAE;MACnB,CAAC,MAAM;QACN,OAAO,KAAK;MACb;IACD,CAAC;IAEDR,IAAI,EAAE,SAAAA,CAAWQ,GAAG,EAAEzC,KAAK,EAAG;MAC7B;MACA,IAAIyC,GAAG,GAAG,IAAI,CAACA,GAAG,CAAEA,GAAG,EAAE,MAAM,CAAE;MACjC,IAAI/H,IAAI,GAAGzB,GAAG,CAAC0J,aAAa,CAAE,IAAI,CAAC1I,IAAI,CAAE,IAAI,CAAC,CAAC;;MAE/C;MACA,IAAK+F,KAAK,KAAK,IAAI,EAAG;QACrB,OAAOtF,IAAI,CAAE+H,GAAG,CAAE;;QAElB;MACD,CAAC,MAAM;QACN/H,IAAI,CAAE+H,GAAG,CAAE,GAAGzC,KAAK;MACpB;;MAEA;MACA,IAAKjH,CAAC,CAAC6J,aAAa,CAAElI,IAAI,CAAE,EAAG;QAC9BA,IAAI,GAAG,IAAI;MACZ;;MAEA;MACAzB,GAAG,CAAC4J,aAAa,CAAE,IAAI,CAAC5I,IAAI,EAAES,IAAI,CAAE;IACrC;EACD,CAAC,CAAE;AACJ,CAAC,EAAIoI,MAAM,CAAE;;;;;;UCn8Bb;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA,eAAe,4BAA4B;WAC3C,eAAe;WACf,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA,8CAA8C;;;;;WCA9C;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;ACNkC;AACQ", "sources": ["webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/pro/_acf-field-flexible-content.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/pro/_acf-field-gallery.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/pro/_acf-field-repeater.js", "webpack://advanced-custom-fields-pro/webpack/bootstrap", "webpack://advanced-custom-fields-pro/webpack/runtime/compat get default export", "webpack://advanced-custom-fields-pro/webpack/runtime/define property getters", "webpack://advanced-custom-fields-pro/webpack/runtime/hasOwnProperty shorthand", "webpack://advanced-custom-fields-pro/webpack/runtime/make namespace object", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/pro/acf-pro-input.js"], "sourcesContent": ["( function ( $ ) {\n\tvar Field = acf.Field.extend( {\n\t\ttype: 'flexible_content',\n\t\twait: '',\n\n\t\tevents: {\n\t\t\t'click [data-name=\"add-layout\"]': 'onClickAdd',\n\t\t\t'click [data-name=\"duplicate-layout\"]': 'onClickDuplicate',\n\t\t\t'click [data-name=\"remove-layout\"]': 'onClickRemove',\n\t\t\t'click [data-name=\"collapse-layout\"]': 'onClickCollapse',\n\t\t\tshowField: 'onShow',\n\t\t\tunloadField: 'onUnload',\n\t\t\tmouseover: 'onHover',\n\t\t},\n\n\t\t$control: function () {\n\t\t\treturn this.$( '.acf-flexible-content:first' );\n\t\t},\n\n\t\t$layoutsWrap: function () {\n\t\t\treturn this.$( '.acf-flexible-content:first > .values' );\n\t\t},\n\n\t\t$layouts: function () {\n\t\t\treturn this.$( '.acf-flexible-content:first > .values > .layout' );\n\t\t},\n\n\t\t$layout: function ( index ) {\n\t\t\treturn this.$(\n\t\t\t\t'.acf-flexible-content:first > .values > .layout:eq(' +\n\t\t\t\t\tindex +\n\t\t\t\t\t')'\n\t\t\t);\n\t\t},\n\n\t\t$clonesWrap: function () {\n\t\t\treturn this.$( '.acf-flexible-content:first > .clones' );\n\t\t},\n\n\t\t$clones: function () {\n\t\t\treturn this.$( '.acf-flexible-content:first > .clones  > .layout' );\n\t\t},\n\n\t\t$clone: function ( name ) {\n\t\t\treturn this.$(\n\t\t\t\t'.acf-flexible-content:first > .clones  > .layout[data-layout=\"' +\n\t\t\t\t\tname +\n\t\t\t\t\t'\"]'\n\t\t\t);\n\t\t},\n\n\t\t$actions: function () {\n\t\t\treturn this.$( '.acf-actions:last' );\n\t\t},\n\n\t\t$button: function () {\n\t\t\treturn this.$( '.acf-actions:last .button' );\n\t\t},\n\n\t\t$popup: function () {\n\t\t\treturn this.$( '.tmpl-popup:last' );\n\t\t},\n\n\t\tgetPopupHTML: function () {\n\t\t\tvar html = this.$popup().html();\n\t\t\tvar $html = $( html );\n\n\t\t\t// count layouts\n\t\t\tvar $layouts = this.$layouts();\n\t\t\tvar countLayouts = function ( name ) {\n\t\t\t\treturn $layouts.filter( function () {\n\t\t\t\t\treturn $( this ).data( 'layout' ) === name;\n\t\t\t\t} ).length;\n\t\t\t};\n\n\t\t\t// modify popup\n\t\t\t$html.find( '[data-layout]' ).each( function () {\n\t\t\t\tvar $a = $( this );\n\t\t\t\tvar min = $a.data( 'min' ) || 0;\n\t\t\t\tvar max = $a.data( 'max' ) || 0;\n\t\t\t\tvar name = $a.data( 'layout' ) || '';\n\t\t\t\tvar count = countLayouts( name );\n\n\t\t\t\t// max\n\t\t\t\tif ( max && count >= max ) {\n\t\t\t\t\t$a.addClass( 'disabled' );\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// min\n\t\t\t\tif ( min && count < min ) {\n\t\t\t\t\tvar required = min - count;\n\t\t\t\t\tvar title = acf.__(\n\t\t\t\t\t\t'{required} {label} {identifier} required (min {min})'\n\t\t\t\t\t);\n\t\t\t\t\tvar identifier = acf._n( 'layout', 'layouts', required );\n\n\t\t\t\t\t// translate\n\t\t\t\t\ttitle = title.replace( '{required}', required );\n\t\t\t\t\ttitle = title.replace( '{label}', name ); // 5.5.0\n\t\t\t\t\ttitle = title.replace( '{identifier}', identifier );\n\t\t\t\t\ttitle = title.replace( '{min}', min );\n\n\t\t\t\t\t// badge\n\t\t\t\t\t$a.append(\n\t\t\t\t\t\t'<span class=\"badge\" title=\"' +\n\t\t\t\t\t\t\ttitle +\n\t\t\t\t\t\t\t'\">' +\n\t\t\t\t\t\t\trequired +\n\t\t\t\t\t\t\t'</span>'\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t} );\n\n\t\t\t// update\n\t\t\thtml = $html.outerHTML();\n\n\t\t\treturn html;\n\t\t},\n\n\t\tgetValue: function () {\n\t\t\treturn this.$layouts().length;\n\t\t},\n\n\t\tallowRemove: function () {\n\t\t\tvar min = parseInt( this.get( 'min' ) );\n\t\t\treturn ! min || min < this.val();\n\t\t},\n\n\t\tallowAdd: function () {\n\t\t\tvar max = parseInt( this.get( 'max' ) );\n\t\t\treturn ! max || max > this.val();\n\t\t},\n\n\t\tisFull: function () {\n\t\t\tvar max = parseInt( this.get( 'max' ) );\n\t\t\treturn max && this.val() >= max;\n\t\t},\n\n\t\taddSortable: function ( self ) {\n\t\t\t// bail early if max 1 row\n\t\t\tif ( this.get( 'max' ) == 1 ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// add sortable\n\t\t\tthis.$layoutsWrap().sortable( {\n\t\t\t\titems: '> .layout',\n\t\t\t\thandle: '> .acf-fc-layout-handle',\n\t\t\t\tforceHelperSize: true,\n\t\t\t\tforcePlaceholderSize: true,\n\t\t\t\tscroll: true,\n\t\t\t\tstop: function ( event, ui ) {\n\t\t\t\t\tself.render();\n\t\t\t\t},\n\t\t\t\tupdate: function ( event, ui ) {\n\t\t\t\t\tself.$input().trigger( 'change' );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\taddCollapsed: function () {\n\t\t\tvar indexes = preference.load( this.get( 'key' ) );\n\n\t\t\t// bail early if no collapsed\n\t\t\tif ( ! indexes ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// loop\n\t\t\tthis.$layouts().each( function ( i ) {\n\t\t\t\tif ( indexes.indexOf( i ) > -1 ) {\n\t\t\t\t\t$( this ).addClass( '-collapsed' );\n\t\t\t\t}\n\t\t\t} );\n\t\t},\n\n\t\taddUnscopedEvents: function ( self ) {\n\t\t\t// invalidField\n\t\t\tthis.on( 'invalidField', '.layout', function ( e ) {\n\t\t\t\tself.onInvalidField( e, $( this ) );\n\t\t\t} );\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\t// add unscoped events\n\t\t\tthis.addUnscopedEvents( this );\n\n\t\t\t// add collapsed\n\t\t\tthis.addCollapsed();\n\n\t\t\t// disable clone\n\t\t\tacf.disable( this.$clonesWrap(), this.cid );\n\n\t\t\t// render\n\t\t\tthis.render();\n\t\t},\n\n\t\trender: function () {\n\t\t\t// update order number\n\t\t\tthis.$layouts().each( function ( i ) {\n\t\t\t\t$( this )\n\t\t\t\t\t.find( '.acf-fc-layout-order:first' )\n\t\t\t\t\t.html( i + 1 );\n\t\t\t} );\n\n\t\t\t// empty\n\t\t\tif ( this.val() == 0 ) {\n\t\t\t\tthis.$control().addClass( '-empty' );\n\t\t\t} else {\n\t\t\t\tthis.$control().removeClass( '-empty' );\n\t\t\t}\n\n\t\t\t// max\n\t\t\tif ( this.isFull() ) {\n\t\t\t\tthis.$button().addClass( 'disabled' );\n\t\t\t} else {\n\t\t\t\tthis.$button().removeClass( 'disabled' );\n\t\t\t}\n\t\t},\n\n\t\tonShow: function ( e, $el, context ) {\n\t\t\t// get sub fields\n\t\t\tvar fields = acf.getFields( {\n\t\t\t\tis: ':visible',\n\t\t\t\tparent: this.$el,\n\t\t\t} );\n\n\t\t\t// trigger action\n\t\t\t// - ignore context, no need to pass through 'conditional_logic'\n\t\t\t// - this is just for fields like google_map to render itself\n\t\t\tacf.doAction( 'show_fields', fields );\n\t\t},\n\n\t\tvalidateAdd: function () {\n\t\t\t// return true if allowed\n\t\t\tif ( this.allowAdd() ) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tvar max = this.get( 'max' );\n\t\t\tvar text = acf.__(\n\t\t\t\t'This field has a limit of {max} {label} {identifier}'\n\t\t\t);\n\t\t\tvar identifier = acf._n( 'layout', 'layouts', max );\n\n\t\t\t// replace\n\t\t\ttext = text.replace( '{max}', max );\n\t\t\ttext = text.replace( '{label}', '' );\n\t\t\ttext = text.replace( '{identifier}', identifier );\n\n\t\t\t// add notice\n\t\t\tthis.showNotice( {\n\t\t\t\ttext: text,\n\t\t\t\ttype: 'warning',\n\t\t\t} );\n\n\t\t\treturn false;\n\t\t},\n\n\t\tonClickAdd: function ( e, $el ) {\n\t\t\t// validate\n\t\t\tif ( ! this.validateAdd() ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// within layout\n\t\t\tvar $layout = null;\n\t\t\tif ( $el.hasClass( 'acf-icon' ) ) {\n\t\t\t\t$layout = $el.closest( '.layout' );\n\t\t\t\t$layout.addClass( '-hover' );\n\t\t\t}\n\n\t\t\t// new popup\n\t\t\tvar popup = new Popup( {\n\t\t\t\ttarget: $el,\n\t\t\t\ttargetConfirm: false,\n\t\t\t\ttext: this.getPopupHTML(),\n\t\t\t\tcontext: this,\n\t\t\t\tconfirm: function ( e, $el ) {\n\t\t\t\t\t// check disabled\n\t\t\t\t\tif ( $el.hasClass( 'disabled' ) ) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\t// add\n\t\t\t\t\tthis.add( {\n\t\t\t\t\t\tlayout: $el.data( 'layout' ),\n\t\t\t\t\t\tbefore: $layout,\n\t\t\t\t\t} );\n\t\t\t\t},\n\t\t\t\tcancel: function () {\n\t\t\t\t\tif ( $layout ) {\n\t\t\t\t\t\t$layout.removeClass( '-hover' );\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t} );\n\n\t\t\t// add extra event\n\t\t\tpopup.on( 'click', '[data-layout]', 'onConfirm' );\n\t\t},\n\n\t\tadd: function ( args ) {\n\t\t\t// defaults\n\t\t\targs = acf.parseArgs( args, {\n\t\t\t\tlayout: '',\n\t\t\t\tbefore: false,\n\t\t\t} );\n\n\t\t\t// validate\n\t\t\tif ( ! this.allowAdd() ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// add row\n\t\t\tvar $el = acf.duplicate( {\n\t\t\t\ttarget: this.$clone( args.layout ),\n\t\t\t\tappend: this.proxy( function ( $el, $el2 ) {\n\t\t\t\t\t// append\n\t\t\t\t\tif ( args.before ) {\n\t\t\t\t\t\targs.before.before( $el2 );\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.$layoutsWrap().append( $el2 );\n\t\t\t\t\t}\n\n\t\t\t\t\t// enable\n\t\t\t\t\tacf.enable( $el2, this.cid );\n\n\t\t\t\t\t// render\n\t\t\t\t\tthis.render();\n\t\t\t\t} ),\n\t\t\t} );\n\n\t\t\t// trigger change for validation errors\n\t\t\tthis.$input().trigger( 'change' );\n\n\t\t\treturn $el;\n\t\t},\n\n\t\tonClickDuplicate: function ( e, $el ) {\n\t\t\t// Validate with warning.\n\t\t\tif ( ! this.validateAdd() ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// get layout and duplicate it.\n\t\t\tvar $layout = $el.closest( '.layout' );\n\t\t\tthis.duplicateLayout( $layout );\n\t\t},\n\n\t\tduplicateLayout: function ( $layout ) {\n\t\t\t// Validate without warning.\n\t\t\tif ( ! this.allowAdd() ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\tvar fieldKey = this.get( 'key' );\n\n\t\t\t// Duplicate layout.\n\t\t\tvar $el = acf.duplicate( {\n\t\t\t\ttarget: $layout,\n\n\t\t\t\t// Provide a custom renaming callback to avoid renaming parent row attributes.\n\t\t\t\trename: function ( name, value, search, replace ) {\n\t\t\t\t\t// Rename id attributes from \"field_1-search\" to \"field_1-replace\".\n\t\t\t\t\tif ( name === 'id' || name === 'for' ) {\n\t\t\t\t\t\treturn value.replace(\n\t\t\t\t\t\t\tfieldKey + '-' + search,\n\t\t\t\t\t\t\tfieldKey + '-' + replace\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\t// Rename name and for attributes from \"[field_1][search]\" to \"[field_1][replace]\".\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn value.replace(\n\t\t\t\t\t\t\tfieldKey + '][' + search,\n\t\t\t\t\t\t\tfieldKey + '][' + replace\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tbefore: function ( $el ) {\n\t\t\t\t\tacf.doAction( 'unmount', $el );\n\t\t\t\t},\n\t\t\t\tafter: function ( $el, $el2 ) {\n\t\t\t\t\tacf.doAction( 'remount', $el );\n\t\t\t\t},\n\t\t\t} );\n\n\t\t\t// trigger change for validation errors\n\t\t\tthis.$input().trigger( 'change' );\n\n\t\t\t// Update order numbers.\n\t\t\tthis.render();\n\n\t\t\t// Draw focus to layout.\n\t\t\tacf.focusAttention( $el );\n\n\t\t\t// Return new layout.\n\t\t\treturn $el;\n\t\t},\n\n\t\tvalidateRemove: function () {\n\t\t\t// return true if allowed\n\t\t\tif ( this.allowRemove() ) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tvar min = this.get( 'min' );\n\t\t\tvar text = acf.__(\n\t\t\t\t'This field requires at least {min} {label} {identifier}'\n\t\t\t);\n\t\t\tvar identifier = acf._n( 'layout', 'layouts', min );\n\n\t\t\t// replace\n\t\t\ttext = text.replace( '{min}', min );\n\t\t\ttext = text.replace( '{label}', '' );\n\t\t\ttext = text.replace( '{identifier}', identifier );\n\n\t\t\t// add notice\n\t\t\tthis.showNotice( {\n\t\t\t\ttext: text,\n\t\t\t\ttype: 'warning',\n\t\t\t} );\n\n\t\t\treturn false;\n\t\t},\n\n\t\tonClickRemove: function ( e, $el ) {\n\t\t\tvar $layout = $el.closest( '.layout' );\n\n\t\t\t// Bypass confirmation when holding down \"shift\" key.\n\t\t\tif ( e.shiftKey ) {\n\t\t\t\treturn this.removeLayout( $layout );\n\t\t\t}\n\n\t\t\t// add class\n\t\t\t$layout.addClass( '-hover' );\n\n\t\t\t// add tooltip\n\t\t\tvar tooltip = acf.newTooltip( {\n\t\t\t\tconfirmRemove: true,\n\t\t\t\ttarget: $el,\n\t\t\t\tcontext: this,\n\t\t\t\tconfirm: function () {\n\t\t\t\t\tthis.removeLayout( $layout );\n\t\t\t\t},\n\t\t\t\tcancel: function () {\n\t\t\t\t\t$layout.removeClass( '-hover' );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tremoveLayout: function ( $layout ) {\n\t\t\t// reference\n\t\t\tvar self = this;\n\n\t\t\tvar endHeight = this.getValue() == 1 ? 60 : 0;\n\n\t\t\t// remove\n\t\t\tacf.remove( {\n\t\t\t\ttarget: $layout,\n\t\t\t\tendHeight: endHeight,\n\t\t\t\tcomplete: function () {\n\t\t\t\t\t// trigger change to allow attachment save\n\t\t\t\t\tself.$input().trigger( 'change' );\n\n\t\t\t\t\t// render\n\t\t\t\t\tself.render();\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tonClickCollapse: function ( e, $el ) {\n\t\t\tvar $layout = $el.closest( '.layout' );\n\n\t\t\t// toggle\n\t\t\tif ( this.isLayoutClosed( $layout ) ) {\n\t\t\t\tthis.openLayout( $layout );\n\t\t\t} else {\n\t\t\t\tthis.closeLayout( $layout );\n\t\t\t}\n\t\t},\n\n\t\tisLayoutClosed: function ( $layout ) {\n\t\t\treturn $layout.hasClass( '-collapsed' );\n\t\t},\n\n\t\topenLayout: function ( $layout ) {\n\t\t\t$layout.removeClass( '-collapsed' );\n\t\t\tacf.doAction( 'show', $layout, 'collapse' );\n\t\t},\n\n\t\tcloseLayout: function ( $layout ) {\n\t\t\t$layout.addClass( '-collapsed' );\n\t\t\tacf.doAction( 'hide', $layout, 'collapse' );\n\n\t\t\t// render\n\t\t\t// - no change could happen if layout was already closed. Only render when closing\n\t\t\tthis.renderLayout( $layout );\n\t\t},\n\n\t\trenderLayout: function ( $layout ) {\n\t\t\tvar $input = $layout.children( 'input' );\n\t\t\tvar prefix = $input.attr( 'name' ).replace( '[acf_fc_layout]', '' );\n\n\t\t\t// ajax data\n\t\t\tvar ajaxData = {\n\t\t\t\taction: 'acf/fields/flexible_content/layout_title',\n\t\t\t\tfield_key: this.get( 'key' ),\n\t\t\t\ti: $layout.index(),\n\t\t\t\tlayout: $layout.data( 'layout' ),\n\t\t\t\tvalue: acf.serialize( $layout, prefix ),\n\t\t\t};\n\n\t\t\t// ajax\n\t\t\t$.ajax( {\n\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\tdata: acf.prepareForAjax( ajaxData ),\n\t\t\t\tdataType: 'html',\n\t\t\t\ttype: 'post',\n\t\t\t\tsuccess: function ( html ) {\n\t\t\t\t\tif ( html ) {\n\t\t\t\t\t\t$layout\n\t\t\t\t\t\t\t.children( '.acf-fc-layout-handle' )\n\t\t\t\t\t\t\t.html( html );\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tonUnload: function () {\n\t\t\tvar indexes = [];\n\n\t\t\t// loop\n\t\t\tthis.$layouts().each( function ( i ) {\n\t\t\t\tif ( $( this ).hasClass( '-collapsed' ) ) {\n\t\t\t\t\tindexes.push( i );\n\t\t\t\t}\n\t\t\t} );\n\n\t\t\t// allow null\n\t\t\tindexes = indexes.length ? indexes : null;\n\n\t\t\t// set\n\t\t\tpreference.save( this.get( 'key' ), indexes );\n\t\t},\n\n\t\tonInvalidField: function ( e, $layout ) {\n\t\t\t// open if is collapsed\n\t\t\tif ( this.isLayoutClosed( $layout ) ) {\n\t\t\t\tthis.openLayout( $layout );\n\t\t\t}\n\t\t},\n\n\t\tonHover: function () {\n\t\t\t// add sortable\n\t\t\tthis.addSortable( this );\n\n\t\t\t// remove event\n\t\t\tthis.off( 'mouseover' );\n\t\t},\n\t} );\n\n\tacf.registerFieldType( Field );\n\n\t/**\n\t *  Popup\n\t *\n\t *  description\n\t *\n\t *  @date\t7/4/18\n\t *  @since\t5.6.9\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tvar Popup = acf.models.TooltipConfirm.extend( {\n\t\tevents: {\n\t\t\t'click [data-layout]': 'onConfirm',\n\t\t\t'click [data-event=\"cancel\"]': 'onCancel',\n\t\t},\n\n\t\trender: function () {\n\t\t\t// set HTML\n\t\t\tthis.html( this.get( 'text' ) );\n\n\t\t\t// add class\n\t\t\tthis.$el.addClass( 'acf-fc-popup' );\n\t\t},\n\t} );\n\n\t/**\n\t *  conditions\n\t *\n\t *  description\n\t *\n\t *  @date\t9/4/18\n\t *  @since\t5.6.9\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\t// register existing conditions\n\tacf.registerConditionForFieldType( 'hasValue', 'flexible_content' );\n\tacf.registerConditionForFieldType( 'hasNoValue', 'flexible_content' );\n\tacf.registerConditionForFieldType( 'lessThan', 'flexible_content' );\n\tacf.registerConditionForFieldType( 'greaterThan', 'flexible_content' );\n\n\t// state\n\tvar preference = new acf.Model( {\n\t\tname: 'this.collapsedLayouts',\n\n\t\tkey: function ( key, context ) {\n\t\t\tvar count = this.get( key + context ) || 0;\n\n\t\t\t// update\n\t\t\tcount++;\n\t\t\tthis.set( key + context, count, true );\n\n\t\t\t// modify fieldKey\n\t\t\tif ( count > 1 ) {\n\t\t\t\tkey += '-' + count;\n\t\t\t}\n\n\t\t\treturn key;\n\t\t},\n\n\t\tload: function ( key ) {\n\t\t\tvar key = this.key( key, 'load' );\n\t\t\tvar data = acf.getPreference( this.name );\n\n\t\t\tif ( data && data[ key ] ) {\n\t\t\t\treturn data[ key ];\n\t\t\t} else {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t},\n\n\t\tsave: function ( key, value ) {\n\t\t\tvar key = this.key( key, 'save' );\n\t\t\tvar data = acf.getPreference( this.name ) || {};\n\n\t\t\t// delete\n\t\t\tif ( value === null ) {\n\t\t\t\tdelete data[ key ];\n\n\t\t\t\t// append\n\t\t\t} else {\n\t\t\t\tdata[ key ] = value;\n\t\t\t}\n\n\t\t\t// allow null\n\t\t\tif ( $.isEmptyObject( data ) ) {\n\t\t\t\tdata = null;\n\t\t\t}\n\n\t\t\t// save\n\t\t\tacf.setPreference( this.name, data );\n\t\t},\n\t} );\n} )( jQuery );\n", "( function ( $ ) {\n\tvar Field = acf.Field.extend( {\n\t\ttype: 'gallery',\n\n\t\tevents: {\n\t\t\t'click .acf-gallery-add': 'onClickAdd',\n\t\t\t'click .acf-gallery-edit': 'onClickEdit',\n\t\t\t'click .acf-gallery-remove': 'onClickRemove',\n\t\t\t'click .acf-gallery-attachment': 'onClickSelect',\n\t\t\t'click .acf-gallery-close': 'onClickClose',\n\t\t\t'change .acf-gallery-sort': 'onChangeSort',\n\t\t\t'click .acf-gallery-update': 'onUpdate',\n\t\t\tmouseover: 'onHover',\n\t\t\tshowField: 'render',\n\t\t},\n\n\t\tactions: {\n\t\t\tvalidation_begin: 'onValidationBegin',\n\t\t\tvalidation_failure: 'onValidationFailure',\n\t\t\tresize: 'onResize',\n\t\t},\n\n\t\tonValidationBegin: function () {\n\t\t\tacf.disable( this.$sideData(), this.cid );\n\t\t},\n\n\t\tonValidationFailure: function () {\n\t\t\tacf.enable( this.$sideData(), this.cid );\n\t\t},\n\n\t\t$control: function () {\n\t\t\treturn this.$( '.acf-gallery' );\n\t\t},\n\n\t\t$collection: function () {\n\t\t\treturn this.$( '.acf-gallery-attachments' );\n\t\t},\n\n\t\t$attachments: function () {\n\t\t\treturn this.$( '.acf-gallery-attachment' );\n\t\t},\n\n\t\t$attachment: function ( id ) {\n\t\t\treturn this.$( '.acf-gallery-attachment[data-id=\"' + id + '\"]' );\n\t\t},\n\n\t\t$active: function () {\n\t\t\treturn this.$( '.acf-gallery-attachment.active' );\n\t\t},\n\n\t\t$main: function () {\n\t\t\treturn this.$( '.acf-gallery-main' );\n\t\t},\n\n\t\t$side: function () {\n\t\t\treturn this.$( '.acf-gallery-side' );\n\t\t},\n\n\t\t$sideData: function () {\n\t\t\treturn this.$( '.acf-gallery-side-data' );\n\t\t},\n\n\t\tisFull: function () {\n\t\t\tvar max = parseInt( this.get( 'max' ) );\n\t\t\tvar count = this.$attachments().length;\n\t\t\treturn max && count >= max;\n\t\t},\n\n\t\tgetValue: function () {\n\t\t\t// vars\n\t\t\tvar val = [];\n\n\t\t\t// loop\n\t\t\tthis.$attachments().each( function () {\n\t\t\t\tval.push( $( this ).data( 'id' ) );\n\t\t\t} );\n\n\t\t\t// return\n\t\t\treturn val.length ? val : false;\n\t\t},\n\n\t\taddUnscopedEvents: function ( self ) {\n\t\t\t// invalidField\n\t\t\tthis.on( 'change', '.acf-gallery-side', function ( e ) {\n\t\t\t\tself.onUpdate( e, $( this ) );\n\t\t\t} );\n\t\t},\n\n\t\taddSortable: function ( self ) {\n\t\t\t// add sortable\n\t\t\tthis.$collection().sortable( {\n\t\t\t\titems: '.acf-gallery-attachment',\n\t\t\t\tforceHelperSize: true,\n\t\t\t\tforcePlaceholderSize: true,\n\t\t\t\tscroll: true,\n\t\t\t\tstart: function ( event, ui ) {\n\t\t\t\t\tui.placeholder.html( ui.item.html() );\n\t\t\t\t\tui.placeholder.removeAttr( 'style' );\n\t\t\t\t},\n\t\t\t\tupdate: function ( event, ui ) {\n\t\t\t\t\tself.$input().trigger( 'change' );\n\t\t\t\t},\n\t\t\t} );\n\n\t\t\t// resizable\n\t\t\tthis.$control().resizable( {\n\t\t\t\thandles: 's',\n\t\t\t\tminHeight: 200,\n\t\t\t\tstop: function ( event, ui ) {\n\t\t\t\t\tacf.update_user_setting( 'gallery_height', ui.size.height );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\t// add unscoped events\n\t\t\tthis.addUnscopedEvents( this );\n\n\t\t\t// render\n\t\t\tthis.render();\n\t\t},\n\n\t\trender: function () {\n\t\t\t// vars\n\t\t\tvar $sort = this.$( '.acf-gallery-sort' );\n\t\t\tvar $add = this.$( '.acf-gallery-add' );\n\t\t\tvar count = this.$attachments().length;\n\n\t\t\t// disable add\n\t\t\tif ( this.isFull() ) {\n\t\t\t\t$add.addClass( 'disabled' );\n\t\t\t} else {\n\t\t\t\t$add.removeClass( 'disabled' );\n\t\t\t}\n\n\t\t\t// disable select\n\t\t\tif ( ! count ) {\n\t\t\t\t$sort.addClass( 'disabled' );\n\t\t\t} else {\n\t\t\t\t$sort.removeClass( 'disabled' );\n\t\t\t}\n\n\t\t\t// resize\n\t\t\tthis.resize();\n\t\t},\n\n\t\tresize: function () {\n\t\t\t// vars\n\t\t\tvar width = this.$control().width();\n\t\t\tvar target = 150;\n\t\t\tvar columns = Math.round( width / target );\n\n\t\t\t// max columns = 8\n\t\t\tcolumns = Math.min( columns, 8 );\n\n\t\t\t// update data\n\t\t\tthis.$control().attr( 'data-columns', columns );\n\t\t},\n\n\t\tonResize: function () {\n\t\t\tthis.resize();\n\t\t},\n\n\t\topenSidebar: function () {\n\t\t\t// add class\n\t\t\tthis.$control().addClass( '-open' );\n\n\t\t\t// hide bulk actions\n\t\t\t// should be done with CSS\n\t\t\t//this.$main().find('.acf-gallery-sort').hide();\n\n\t\t\t// vars\n\t\t\tvar width = this.$control().width() / 3;\n\t\t\twidth = parseInt( width );\n\t\t\twidth = Math.max( width, 350 );\n\n\t\t\t// animate\n\t\t\tthis.$( '.acf-gallery-side-inner' ).css( { width: width - 1 } );\n\t\t\tthis.$side().animate( { width: width - 1 }, 250 );\n\t\t\tthis.$main().animate( { right: width }, 250 );\n\t\t},\n\n\t\tcloseSidebar: function () {\n\t\t\t// remove class\n\t\t\tthis.$control().removeClass( '-open' );\n\n\t\t\t// clear selection\n\t\t\tthis.$active().removeClass( 'active' );\n\n\t\t\t// disable sidebar\n\t\t\tacf.disable( this.$side() );\n\n\t\t\t// animate\n\t\t\tvar $sideData = this.$( '.acf-gallery-side-data' );\n\t\t\tthis.$main().animate( { right: 0 }, 250 );\n\t\t\tthis.$side().animate( { width: 0 }, 250, function () {\n\t\t\t\t$sideData.html( '' );\n\t\t\t} );\n\t\t},\n\n\t\tonClickAdd: function ( e, $el ) {\n\t\t\t// validate\n\t\t\tif ( this.isFull() ) {\n\t\t\t\tthis.showNotice( {\n\t\t\t\t\ttext: acf.__( 'Maximum selection reached' ),\n\t\t\t\t\ttype: 'warning',\n\t\t\t\t} );\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// new frame\n\t\t\tvar frame = acf.newMediaPopup( {\n\t\t\t\tmode: 'select',\n\t\t\t\ttitle: acf.__( 'Add Image to Gallery' ),\n\t\t\t\tfield: this.get( 'key' ),\n\t\t\t\tmultiple: 'add',\n\t\t\t\tlibrary: this.get( 'library' ),\n\t\t\t\tallowedTypes: this.get( 'mime_types' ),\n\t\t\t\tselected: this.val(),\n\t\t\t\tselect: $.proxy( function ( attachment, i ) {\n\t\t\t\t\tthis.appendAttachment( attachment, i );\n\t\t\t\t}, this ),\n\t\t\t} );\n\t\t},\n\n\t\tappendAttachment: function ( attachment, i ) {\n\t\t\t// vars\n\t\t\tattachment = this.validateAttachment( attachment );\n\n\t\t\t// bail early if is full\n\t\t\tif ( this.isFull() ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// bail early if already exists\n\t\t\tif ( this.$attachment( attachment.id ).length ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// html\n\t\t\tvar html = [\n\t\t\t\t'<div class=\"acf-gallery-attachment\" data-id=\"' +\n\t\t\t\t\tattachment.id +\n\t\t\t\t\t'\">',\n\t\t\t\t'<input type=\"hidden\" value=\"' +\n\t\t\t\t\tattachment.id +\n\t\t\t\t\t'\" name=\"' +\n\t\t\t\t\tthis.getInputName() +\n\t\t\t\t\t'[]\">',\n\t\t\t\t'<div class=\"margin\" title=\"\">',\n\t\t\t\t'<div class=\"thumbnail\">',\n\t\t\t\t'<img src=\"\" alt=\"\">',\n\t\t\t\t'</div>',\n\t\t\t\t'<div class=\"filename\"></div>',\n\t\t\t\t'</div>',\n\t\t\t\t'<div class=\"actions\">',\n\t\t\t\t'<a href=\"#\" class=\"acf-icon -cancel dark acf-gallery-remove\" data-id=\"' +\n\t\t\t\t\tattachment.id +\n\t\t\t\t\t'\"></a>',\n\t\t\t\t'</div>',\n\t\t\t\t'</div>',\n\t\t\t].join( '' );\n\t\t\tvar $html = $( html );\n\n\t\t\t// append\n\t\t\tthis.$collection().append( $html );\n\n\t\t\t// move to beginning\n\t\t\tif ( this.get( 'insert' ) === 'prepend' ) {\n\t\t\t\tvar $before = this.$attachments().eq( i );\n\t\t\t\tif ( $before.length ) {\n\t\t\t\t\t$before.before( $html );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// render attachment\n\t\t\tthis.renderAttachment( attachment );\n\n\t\t\t// render\n\t\t\tthis.render();\n\n\t\t\t// trigger change\n\t\t\tthis.$input().trigger( 'change' );\n\t\t},\n\n\t\tvalidateAttachment: function ( attachment ) {\n\t\t\t// defaults\n\t\t\tattachment = acf.parseArgs( attachment, {\n\t\t\t\tid: '',\n\t\t\t\turl: '',\n\t\t\t\talt: '',\n\t\t\t\ttitle: '',\n\t\t\t\tfilename: '',\n\t\t\t\ttype: 'image',\n\t\t\t} );\n\n\t\t\t// WP attachment\n\t\t\tif ( attachment.attributes ) {\n\t\t\t\tattachment = attachment.attributes;\n\n\t\t\t\t// preview size\n\t\t\t\tvar url = acf.isget(\n\t\t\t\t\tattachment,\n\t\t\t\t\t'sizes',\n\t\t\t\t\tthis.get( 'preview_size' ),\n\t\t\t\t\t'url'\n\t\t\t\t);\n\t\t\t\tif ( url !== null ) {\n\t\t\t\t\tattachment.url = url;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// return\n\t\t\treturn attachment;\n\t\t},\n\n\t\trenderAttachment: function ( attachment ) {\n\t\t\t// vars\n\t\t\tattachment = this.validateAttachment( attachment );\n\n\t\t\t// vars\n\t\t\tvar $el = this.$attachment( attachment.id );\n\n\t\t\t// Image type.\n\t\t\tif ( attachment.type == 'image' ) {\n\t\t\t\t// Remove filename.\n\t\t\t\t$el.find( '.filename' ).remove();\n\n\t\t\t\t// Other file type.\n\t\t\t} else {\n\t\t\t\t// Check for attachment featured image.\n\t\t\t\tvar image = acf.isget( attachment, 'image', 'src' );\n\t\t\t\tif ( image !== null ) {\n\t\t\t\t\tattachment.url = image;\n\t\t\t\t}\n\n\t\t\t\t// Update filename text.\n\t\t\t\t$el.find( '.filename' ).text( attachment.filename );\n\t\t\t}\n\n\t\t\t// Default to mimetype icon.\n\t\t\tif ( ! attachment.url ) {\n\t\t\t\tattachment.url = acf.get( 'mimeTypeIcon' );\n\t\t\t\t$el.addClass( '-icon' );\n\t\t\t}\n\n\t\t\t// update els\n\t\t\t$el.find( 'img' ).attr( {\n\t\t\t\tsrc: attachment.url,\n\t\t\t\talt: attachment.alt,\n\t\t\t\ttitle: attachment.title,\n\t\t\t} );\n\n\t\t\t// update val\n\t\t\tacf.val( $el.find( 'input' ), attachment.id );\n\t\t},\n\n\t\teditAttachment: function ( id ) {\n\t\t\t// new frame\n\t\t\tvar frame = acf.newMediaPopup( {\n\t\t\t\tmode: 'edit',\n\t\t\t\ttitle: acf.__( 'Edit Image' ),\n\t\t\t\tbutton: acf.__( 'Update Image' ),\n\t\t\t\tattachment: id,\n\t\t\t\tfield: this.get( 'key' ),\n\t\t\t\tselect: $.proxy( function ( attachment, i ) {\n\t\t\t\t\tthis.renderAttachment( attachment );\n\t\t\t\t\t// todo - render sidebar\n\t\t\t\t}, this ),\n\t\t\t} );\n\t\t},\n\n\t\tonClickEdit: function ( e, $el ) {\n\t\t\tvar id = $el.data( 'id' );\n\t\t\tif ( id ) {\n\t\t\t\tthis.editAttachment( id );\n\t\t\t}\n\t\t},\n\n\t\tremoveAttachment: function ( id ) {\n\t\t\t// close sidebar (if open)\n\t\t\tthis.closeSidebar();\n\n\t\t\t// remove attachment\n\t\t\tthis.$attachment( id ).remove();\n\n\t\t\t// render\n\t\t\tthis.render();\n\n\t\t\t// trigger change\n\t\t\tthis.$input().trigger( 'change' );\n\t\t},\n\n\t\tonClickRemove: function ( e, $el ) {\n\t\t\t// prevent event from triggering click on attachment\n\t\t\te.preventDefault();\n\t\t\te.stopPropagation();\n\n\t\t\t//remove\n\t\t\tvar id = $el.data( 'id' );\n\t\t\tif ( id ) {\n\t\t\t\tthis.removeAttachment( id );\n\t\t\t}\n\t\t},\n\n\t\tselectAttachment: function ( id ) {\n\t\t\t// vars\n\t\t\tvar $el = this.$attachment( id );\n\n\t\t\t// bail early if already active\n\t\t\tif ( $el.hasClass( 'active' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// step 1\n\t\t\tvar step1 = this.proxy( function () {\n\t\t\t\t// save any changes in sidebar\n\t\t\t\tthis.$side().find( ':focus' ).trigger( 'blur' );\n\n\t\t\t\t// clear selection\n\t\t\t\tthis.$active().removeClass( 'active' );\n\n\t\t\t\t// add selection\n\t\t\t\t$el.addClass( 'active' );\n\n\t\t\t\t// open sidebar\n\t\t\t\tthis.openSidebar();\n\n\t\t\t\t// call step 2\n\t\t\t\tstep2();\n\t\t\t} );\n\n\t\t\t// step 2\n\t\t\tvar step2 = this.proxy( function () {\n\t\t\t\t// ajax\n\t\t\t\tvar ajaxData = {\n\t\t\t\t\taction: 'acf/fields/gallery/get_attachment',\n\t\t\t\t\tfield_key: this.get( 'key' ),\n\t\t\t\t\tid: id,\n\t\t\t\t};\n\n\t\t\t\t// abort prev ajax call\n\t\t\t\tif ( this.has( 'xhr' ) ) {\n\t\t\t\t\tthis.get( 'xhr' ).abort();\n\t\t\t\t}\n\n\t\t\t\t// loading\n\t\t\t\tacf.showLoading( this.$sideData() );\n\n\t\t\t\t// get HTML\n\t\t\t\tvar xhr = $.ajax( {\n\t\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\t\tdata: acf.prepareForAjax( ajaxData ),\n\t\t\t\t\ttype: 'post',\n\t\t\t\t\tdataType: 'html',\n\t\t\t\t\tcache: false,\n\t\t\t\t\tsuccess: step3,\n\t\t\t\t} );\n\n\t\t\t\t// update\n\t\t\t\tthis.set( 'xhr', xhr );\n\t\t\t} );\n\n\t\t\t// step 3\n\t\t\tvar step3 = this.proxy( function ( html ) {\n\t\t\t\t// bail early if no html\n\t\t\t\tif ( ! html ) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// vars\n\t\t\t\tvar $side = this.$sideData();\n\n\t\t\t\t// render\n\t\t\t\t$side.html( html );\n\n\t\t\t\t// remove acf form data\n\t\t\t\t$side.find( '.compat-field-acf-form-data' ).remove();\n\n\t\t\t\t// merge tables\n\t\t\t\t$side\n\t\t\t\t\t.find( '> table.form-table > tbody' )\n\t\t\t\t\t.append(\n\t\t\t\t\t\t$side.find( '> .compat-attachment-fields > tbody > tr' )\n\t\t\t\t\t);\n\n\t\t\t\t// setup fields\n\t\t\t\tacf.doAction( 'append', $side );\n\t\t\t} );\n\n\t\t\t// run step 1\n\t\t\tstep1();\n\t\t},\n\n\t\tonClickSelect: function ( e, $el ) {\n\t\t\tvar id = $el.data( 'id' );\n\t\t\tif ( id ) {\n\t\t\t\tthis.selectAttachment( id );\n\t\t\t}\n\t\t},\n\n\t\tonClickClose: function ( e, $el ) {\n\t\t\tthis.closeSidebar();\n\t\t},\n\n\t\tonChangeSort: function ( e, $el ) {\n\t\t\t// Bail early if is disabled.\n\t\t\tif ( $el.hasClass( 'disabled' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Get sort val.\n\t\t\tvar val = $el.val();\n\t\t\tif ( ! val ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// find ids\n\t\t\tvar ids = [];\n\t\t\tthis.$attachments().each( function () {\n\t\t\t\tids.push( $( this ).data( 'id' ) );\n\t\t\t} );\n\n\t\t\t// step 1\n\t\t\tvar step1 = this.proxy( function () {\n\t\t\t\t// vars\n\t\t\t\tvar ajaxData = {\n\t\t\t\t\taction: 'acf/fields/gallery/get_sort_order',\n\t\t\t\t\tfield_key: this.get( 'key' ),\n\t\t\t\t\tids: ids,\n\t\t\t\t\tsort: val,\n\t\t\t\t};\n\n\t\t\t\t// get results\n\t\t\t\tvar xhr = $.ajax( {\n\t\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\t\tdataType: 'json',\n\t\t\t\t\ttype: 'post',\n\t\t\t\t\tcache: false,\n\t\t\t\t\tdata: acf.prepareForAjax( ajaxData ),\n\t\t\t\t\tsuccess: step2,\n\t\t\t\t} );\n\t\t\t} );\n\n\t\t\t// step 2\n\t\t\tvar step2 = this.proxy( function ( json ) {\n\t\t\t\t// validate\n\t\t\t\tif ( ! acf.isAjaxSuccess( json ) ) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// reverse order\n\t\t\t\tjson.data.reverse();\n\n\t\t\t\t// loop\n\t\t\t\tjson.data.map( function ( id ) {\n\t\t\t\t\tthis.$collection().prepend( this.$attachment( id ) );\n\t\t\t\t}, this );\n\t\t\t} );\n\n\t\t\t// call step 1\n\t\t\tstep1();\n\t\t},\n\n\t\tonUpdate: function ( e, $el ) {\n\t\t\t// vars\n\t\t\tvar $submit = this.$( '.acf-gallery-update' );\n\n\t\t\t// validate\n\t\t\tif ( $submit.hasClass( 'disabled' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// serialize data\n\t\t\tvar ajaxData = acf.serialize( this.$sideData() );\n\n\t\t\t// loading\n\t\t\t$submit.addClass( 'disabled' );\n\t\t\t$submit.before( '<i class=\"acf-loading\"></i> ' );\n\n\t\t\t// append AJAX action\n\t\t\tajaxData.action = 'acf/fields/gallery/update_attachment';\n\n\t\t\t// ajax\n\t\t\t$.ajax( {\n\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\tdata: acf.prepareForAjax( ajaxData ),\n\t\t\t\ttype: 'post',\n\t\t\t\tdataType: 'json',\n\t\t\t\tcomplete: function () {\n\t\t\t\t\t$submit.removeClass( 'disabled' );\n\t\t\t\t\t$submit.prev( '.acf-loading' ).remove();\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tonHover: function () {\n\t\t\t// add sortable\n\t\t\tthis.addSortable( this );\n\n\t\t\t// remove event\n\t\t\tthis.off( 'mouseover' );\n\t\t},\n\t} );\n\n\tacf.registerFieldType( Field );\n\n\t// register existing conditions\n\tacf.registerConditionForFieldType( 'hasValue', 'gallery' );\n\tacf.registerConditionForFieldType( 'hasNoValue', 'gallery' );\n\tacf.registerConditionForFieldType( 'selectionLessThan', 'gallery' );\n\tacf.registerConditionForFieldType( 'selectionGreaterThan', 'gallery' );\n} )( jQuery );\n", "( function ( $ ) {\n\tvar Field = acf.Field.extend( {\n\t\ttype: 'repeater',\n\t\twait: '',\n\t\tpage: 1,\n\t\tnextRowNum: 0,\n\n\t\tevents: {\n\t\t\t'click a[data-event=\"add-row\"]': 'onClickAdd',\n\t\t\t'click a[data-event=\"duplicate-row\"]': 'onClickDuplicate',\n\t\t\t'click a[data-event=\"remove-row\"]': 'onClickRemove',\n\t\t\t'click a[data-event=\"collapse-row\"]': 'onClickCollapse',\n\t\t\t'click a[data-event=\"first-page\"]:not(.disabled)' : 'onClickFirstPage',\n\t\t\t'click a[data-event=\"last-page\"]:not(.disabled)' : 'onClickLastPage',\n\t\t\t'click a[data-event=\"prev-page\"]:not(.disabled)': 'onClickPrevPage',\n\t\t\t'click a[data-event=\"next-page\"]:not(.disabled)': 'onClickNextPage',\n\t\t\t'change .current-page': 'onChangeCurrentPage',\n\t\t\t'click .acf-order-input-wrap': 'onClickRowOrder',\n\t\t\t'blur .acf-order-input': 'onBlurRowOrder',\n\t\t\t'change .acf-order-input': 'onChangeRowOrder',\n\t\t\t'changed:total_rows': 'onChangeTotalRows',\n\t\t\tshowField: 'onShow',\n\t\t\tunloadField: 'onUnload',\n\t\t\tmouseover: 'onHover',\n\t\t\tchange: 'onChangeField',\n\t\t},\n\n\t\t$control: function () {\n\t\t\treturn this.$( '.acf-repeater:first' );\n\t\t},\n\n\t\t$table: function () {\n\t\t\treturn this.$( 'table:first' );\n\t\t},\n\n\t\t$tbody: function () {\n\t\t\treturn this.$( 'tbody:first' );\n\t\t},\n\n\t\t$rows: function () {\n\t\t\treturn this.$( 'tbody:first > tr' ).not( '.acf-clone, .acf-deleted' );\n\t\t},\n\n\t\t$row: function ( index ) {\n\t\t\treturn this.$( 'tbody:first > tr:eq(' + index + ')' );\n\t\t},\n\n\t\t$clone: function () {\n\t\t\treturn this.$( 'tbody:first > tr.acf-clone' );\n\t\t},\n\n\t\t$actions: function () {\n\t\t\treturn this.$( '.acf-actions:last' );\n\t\t},\n\n\t\t$button: function () {\n\t\t\treturn this.$( '.acf-actions:last .button' );\n\t\t},\n\n\t\t$firstPageButton: function() {\n\t\t\treturn this.$( '.acf-tablenav:last .first-page' );\n\t\t},\n\n\t\t$prevPageButton: function() {\n\t\t\treturn this.$( '.acf-tablenav:last .prev-page' );\n\t\t},\n\n\t\t$nextPageButton: function() {\n\t\t\treturn this.$( '.acf-tablenav:last .next-page' );\n\t\t},\n\n\t\t$lastPageButton: function() {\n\t\t\treturn this.$( '.acf-tablenav:last .last-page' );\n\t\t},\n\n\t\t$pageInput: function() {\n\t\t\treturn this.$( '.current-page:last' );\n\t\t},\n\n\t\ttotalPages: function() {\n\t\t\tconst totalPages = this.$( '.acf-total-pages:last' ).text();\n\t\t\treturn parseInt( totalPages );\n\t\t},\n\n\t\tgetValue: function () {\n\t\t\treturn this.$rows().length;\n\t\t},\n\n\t\tallowRemove: function () {\n\t\t\tlet numRows = this.val();\n\t\t\tlet minRows = parseInt( this.get( 'min' ) );\n\n\t\t\tif ( this.get( 'pagination' ) ) {\n\t\t\t\tnumRows = this.get( 'total_rows' );\n\t\t\t}\n\n\t\t\treturn ! minRows || minRows < numRows;\n\t\t},\n\n\t\tallowAdd: function () {\n\t\t\tlet numRows = this.val();\n\t\t\tlet maxRows = parseInt( this.get( 'max' ) );\n\n\t\t\tif ( this.get( 'pagination' ) ) {\n\t\t\t\tnumRows = this.get( 'total_rows' );\n\t\t\t}\n\n\t\t\treturn ! maxRows || maxRows > numRows;\n\t\t},\n\n\t\taddSortable: function ( self ) {\n\t\t\t// bail early if max 1 row\n\t\t\tif ( this.get( 'max' ) == 1 ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Bail early if using pagination.\n\t\t\tif ( this.get( 'pagination') ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// add sortable\n\t\t\tthis.$tbody().sortable( {\n\t\t\t\titems: '> tr',\n\t\t\t\thandle: '> td.order',\n\t\t\t\tforceHelperSize: true,\n\t\t\t\tforcePlaceholderSize: true,\n\t\t\t\tscroll: true,\n\t\t\t\tstop: function ( event, ui ) {\n\t\t\t\t\tself.render();\n\t\t\t\t},\n\t\t\t\tupdate: function ( event, ui ) {\n\t\t\t\t\tself.$input().trigger( 'change' );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\taddCollapsed: function () {\n\t\t\t// vars\n\t\t\tvar indexes = preference.load( this.get( 'key' ) );\n\n\t\t\t// bail early if no collapsed\n\t\t\tif ( ! indexes ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// loop\n\t\t\tthis.$rows().each( function ( i ) {\n\t\t\t\tif ( indexes.indexOf( i ) > -1 ) {\n\t\t\t\t\tif ( $( this ).find( '.-collapsed-target' ).length ) {\n\t\t\t\t\t\t$( this ).addClass( '-collapsed' );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} );\n\t\t},\n\n\t\taddUnscopedEvents: function ( self ) {\n\t\t\t// invalidField\n\t\t\tthis.on( 'invalidField', '.acf-row', function ( e ) {\n\t\t\t\tvar $row = $( this );\n\t\t\t\tif ( self.isCollapsed( $row ) ) {\n\t\t\t\t\tself.expand( $row );\n\t\t\t\t}\n\t\t\t} );\n\n\t\t\t// Listen for changes to fields, so we can persist them in the DOM.\n\t\t\tif ( this.get( 'pagination' ) ) {\n\t\t\t\tthis.on( 'change', 'input, select, textarea', function ( e ) {\n\t\t\t\t\tconst $changed = $( e.currentTarget );\n\t\t\t\t\tif ( ! $changed.hasClass( 'acf-order-input' ) && ! $changed.hasClass( 'acf-row-status' ) ) {\n\t\t\t\t\t\tself.onChangeField( e, $( this ) );\n\t\t\t\t\t}\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\tthis.listenForSavedMetaBoxes();\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\t// add unscoped events\n\t\t\tthis.addUnscopedEvents( this );\n\n\t\t\t// add collapsed\n\t\t\tthis.addCollapsed();\n\n\t\t\t// disable clone\n\t\t\tacf.disable( this.$clone(), this.cid );\n\n\t\t\t// Set up the next row number.\n\t\t\tif ( this.get( 'pagination' ) ) {\n\t\t\t\tthis.nextRowNum = this.get( 'total_rows' );\n\t\t\t}\n\n\t\t\t// render\n\t\t\tthis.render();\n\t\t},\n\n\t\trender: function ( update_order_numbers = true ) {\n\t\t\t// Update order number.\n\t\t\tif ( update_order_numbers ) {\n\t\t\t\tthis.$rows().each( function ( i ) {\n\t\t\t\t\t$( this )\n\t\t\t\t\t\t.find( '> .order > span' )\n\t\t\t\t\t\t.html( i + 1 );\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\t// Extract vars.\n\t\t\tvar $control = this.$control();\n\t\t\tvar $button = this.$button();\n\n\t\t\t// empty\n\t\t\tif ( this.val() == 0 ) {\n\t\t\t\t$control.addClass( '-empty' );\n\t\t\t} else {\n\t\t\t\t$control.removeClass( '-empty' );\n\t\t\t}\n\n\t\t\t// Reached max rows.\n\t\t\tif ( ! this.allowAdd() ) {\n\t\t\t\t$control.addClass( '-max' );\n\t\t\t\t$button.addClass( 'disabled' );\n\t\t\t} else {\n\t\t\t\t$control.removeClass( '-max' );\n\t\t\t\t$button.removeClass( 'disabled' );\n\t\t\t}\n\n\t\t\tif ( this.get( 'pagination' ) ) {\n\t\t\t\tthis.maybeDisablePagination();\n\t\t\t}\n\n\t\t\t// Reached min rows (not used).\n\t\t\t//if( !this.allowRemove() ) {\n\t\t\t//\t$control.addClass('-min');\n\t\t\t//} else {\n\t\t\t//\t$control.removeClass('-min');\n\t\t\t//}\n\t\t},\n\n\t\tlistenForSavedMetaBoxes: function() {\n\t\t\tif ( ! acf.isGutenberg() || ! this.get( 'pagination' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tlet checkedMetaBoxes = true;\n\t\t\twp.data.subscribe( () => {\n\t\t\t\tif ( wp.data.select( 'core/edit-post' ).isSavingMetaBoxes() ) {\n\t\t\t\t\tcheckedMetaBoxes = false;\n\t\t\t\t} else {\n\t\t\t\t\tif ( ! checkedMetaBoxes ) {\n\t\t\t\t\t\tcheckedMetaBoxes = true;\n\t\t\t\t\t\tthis.set( 'total_rows', 0, true );\n\t\t\t\t\t\tthis.ajaxLoadPage( true );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} );\n\t\t},\n\n\t\tincrementTotalRows: function() {\n\t\t\tlet totalRows = this.get( 'total_rows' );\n\t\t\tthis.set( 'total_rows', ++totalRows, true );\n\t\t},\n\n\t\tdecrementTotalRows: function() {\n\t\t\tlet totalRows = this.get( 'total_rows' );\n\t\t\tthis.set( 'total_rows', --totalRows, true );\n\t\t},\n\n\t\tvalidateAdd: function () {\n\t\t\t// return true if allowed\n\t\t\tif ( this.allowAdd() ) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\t// vars\n\t\t\tvar max = this.get( 'max' );\n\t\t\tvar text = acf.__( 'Maximum rows reached ({max} rows)' );\n\n\t\t\t// replace\n\t\t\ttext = text.replace( '{max}', max );\n\n\t\t\t// add notice\n\t\t\tthis.showNotice( {\n\t\t\t\ttext: text,\n\t\t\t\ttype: 'warning',\n\t\t\t} );\n\n\t\t\t// return\n\t\t\treturn false;\n\t\t},\n\n\t\tonClickAdd: function ( e, $el ) {\n\t\t\t// validate\n\t\t\tif ( ! this.validateAdd() ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// add above row\n\t\t\tif ( $el.hasClass( 'acf-icon' ) ) {\n\t\t\t\tthis.add( {\n\t\t\t\t\tbefore: $el.closest( '.acf-row' ),\n\t\t\t\t} );\n\n\t\t\t\t// default\n\t\t\t} else {\n\t\t\t\tthis.add();\n\t\t\t}\n\t\t},\n\n\t\tadd: function ( args ) {\n\t\t\t// validate\n\t\t\tif ( ! this.allowAdd() ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// defaults\n\t\t\targs = acf.parseArgs( args, {\n\t\t\t\tbefore: false,\n\t\t\t} );\n\n\t\t\t// add row\n\t\t\tvar $el = acf.duplicate( {\n\t\t\t\ttarget: this.$clone(),\n\t\t\t\tappend: this.proxy( function ( $el, $el2 ) {\n\t\t\t\t\t// append\n\t\t\t\t\tif ( args.before ) {\n\t\t\t\t\t\targs.before.before( $el2 );\n\t\t\t\t\t} else {\n\t\t\t\t\t\t$el.before( $el2 );\n\t\t\t\t\t}\n\n\t\t\t\t\t// remove clone class\n\t\t\t\t\t$el2.removeClass( 'acf-clone' );\n\n\t\t\t\t\t// enable\n\t\t\t\t\tacf.enable( $el2, this.cid );\n\t\t\t\t} ),\n\t\t\t} );\n\n\t\t\tif ( this.get( 'pagination' ) ) {\n\t\t\t\tthis.incrementTotalRows();\n\n\t\t\t\tif ( false !== args.before ) {\n\t\t\t\t\t// If the row was inserted above an existing row, try to keep that order.\n\t\t\t\t\tconst prevRowNum = parseInt( args.before.find( '.acf-row-number' ).first().text() ) || 0;\n\t\t\t\t\tlet newRowNum = prevRowNum;\n\n\t\t\t\t\tif ( newRowNum && ! args.before.hasClass( 'acf-inserted' ) && ! args.before.hasClass( 'acf-added' ) ) {\n\t\t\t\t\t\t--newRowNum;\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( args.before.hasClass( 'acf-divider' ) ) {\n\t\t\t\t\t\targs.before.removeClass( 'acf-divider' );\n\t\t\t\t\t\t$el.addClass( 'acf-divider' );\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.updateRowStatus( $el, 'inserted' );\n\t\t\t\t\tthis.updateRowStatus( $el, 'reordered', newRowNum );\n\n\t\t\t\t\t// Hide the row numbers to avoid confusion with existing rows.\n\t\t\t\t\t$el.find( '.acf-row-number' ).first().hide().text( newRowNum );\n\t\t\t\t\tif ( ! $el.find( '.acf-order-input-wrap' ).hasClass( 'disabled' ) ) {\n\t\t\t\t\t\tlet message =  acf.__( 'Order will be assigned upon save' );\n\t\t\t\t\t\t$el.find( '.acf-order-input-wrap' ).addClass( 'disabled' );\n\t\t\t\t\t\t$el.find( '.acf-row-number' ).first().after( '<span title=\"' + message + '\">-</span>' );\n\t\t\t\t\t}\n\t\t\t\t\t$el.find( '.acf-order-input' ).first().hide();\n\t\t\t\t\t$el.attr( 'data-inserted', newRowNum );\n\t\t\t\t} else {\n\t\t\t\t\tthis.nextRowNum++;\n\n\t\t\t\t\t$el.find( '.acf-order-input' ).first().val( this.nextRowNum );\n\t\t\t\t\t$el.find( '.acf-row-number' ).first().text( this.nextRowNum );\n\t\t\t\t\tthis.updateRowStatus( $el, 'added' );\n\n\t\t\t\t\tif ( ! this.$tbody().find( '.acf-divider' ).length ) {\n\t\t\t\t\t\t$el.addClass( 'acf-divider' );\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t$el.find( '.acf-input:first' )\n\t\t\t\t\t.find( 'input:not([type=hidden]), select, textarea' )\n\t\t\t\t\t.first()\n\t\t\t\t\t.trigger( 'focus' );\n\t\t\t}\n\n\t\t\t// Render and trigger change for validation errors.\n\t\t\tthis.render();\n\t\t\tthis.$input().trigger( 'change' );\n\n\t\t\treturn $el;\n\t\t},\n\n\t\tonClickDuplicate: function ( e, $el ) {\n\t\t\t// Validate with warning.\n\t\t\tif ( ! this.validateAdd() ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// get layout and duplicate it.\n\t\t\tvar $row = $el.closest( '.acf-row' );\n\t\t\tthis.duplicateRow( $row );\n\t\t},\n\n\t\tduplicateRow: function ( $row ) {\n\t\t\t// Validate without warning.\n\t\t\tif ( ! this.allowAdd() ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// Vars.\n\t\t\tvar fieldKey = this.get( 'key' );\n\n\t\t\t// Duplicate row.\n\t\t\tvar $el = acf.duplicate( {\n\t\t\t\ttarget: $row,\n\n\t\t\t\t// Provide a custom renaming callback to avoid renaming parent row attributes.\n\t\t\t\trename: function ( name, value, search, replace ) {\n\t\t\t\t\t// Rename id attributes from \"field_1-search\" to \"field_1-replace\".\n\t\t\t\t\tif ( name === 'id' || name === 'for' ) {\n\t\t\t\t\t\treturn value.replace(\n\t\t\t\t\t\t\tfieldKey + '-' + search,\n\t\t\t\t\t\t\tfieldKey + '-' + replace\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\t// Rename name and for attributes from \"[field_1][search]\" to \"[field_1][replace]\".\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn value.replace(\n\t\t\t\t\t\t\tfieldKey + '][' + search,\n\t\t\t\t\t\t\tfieldKey + '][' + replace\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tbefore: function ( $el ) {\n\t\t\t\t\tacf.doAction( 'unmount', $el );\n\t\t\t\t},\n\t\t\t\tafter: function ( $el, $el2 ) {\n\t\t\t\t\tacf.doAction( 'remount', $el );\n\t\t\t\t},\n\t\t\t} );\n\n\t\t\tif ( this.get( 'pagination' ) ) {\n\t\t\t\tthis.incrementTotalRows();\n\n\t\t\t\t// If the row was inserted above an existing row, try to keep that order.\n\t\t\t\tconst prevRowNum = parseInt( $row.find( '.acf-row-number' ).first().text() ) || 0;\n\n\t\t\t\tthis.updateRowStatus( $el, 'inserted' );\n\t\t\t\tthis.updateRowStatus( $el, 'reordered', prevRowNum );\n\n\t\t\t\t// Hide the row numbers to avoid confusion with existing rows.\n\t\t\t\t$el.find( '.acf-row-number' ).first().hide();\n\t\t\t\tif ( ! $el.find( '.acf-order-input-wrap' ).hasClass( 'disabled' ) ) {\n\t\t\t\t\tlet message =  acf.__( 'Order will be assigned upon save' );\n\t\t\t\t\t$el.find( '.acf-order-input-wrap' ).addClass( 'disabled' );\n\t\t\t\t\t$el.find( '.acf-row-number' ).first().after( '<span title=\"' + message + '\">-</span>' );\n\t\t\t\t}\n\t\t\t\t$el.find( '.acf-order-input' ).first().hide();\n\t\t\t\t$el.attr( 'data-inserted', prevRowNum );\n\t\t\t\t$el.removeClass( 'acf-divider' );\n\t\t\t}\n\n\t\t\t// trigger change for validation errors\n\t\t\tthis.$input().trigger( 'change' );\n\n\t\t\t// Update order numbers.\n\t\t\tthis.render();\n\n\t\t\t// Focus on new row.\n\t\t\tacf.focusAttention( $el );\n\n\t\t\t// Return new layout.\n\t\t\treturn $el;\n\t\t},\n\n\t\tvalidateRemove: function () {\n\t\t\t// return true if allowed\n\t\t\tif ( this.allowRemove() ) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\t// vars\n\t\t\tvar min = this.get( 'min' );\n\t\t\tvar text = acf.__( 'Minimum rows not reached ({min} rows)' );\n\n\t\t\t// replace\n\t\t\ttext = text.replace( '{min}', min );\n\n\t\t\t// add notice\n\t\t\tthis.showNotice( {\n\t\t\t\ttext: text,\n\t\t\t\ttype: 'warning',\n\t\t\t} );\n\n\t\t\t// return\n\t\t\treturn false;\n\t\t},\n\n\t\tonClickRemove: function ( e, $el ) {\n\t\t\tvar $row = $el.closest( '.acf-row' );\n\n\t\t\t// Bypass confirmation when holding down \"shift\" key.\n\t\t\tif ( e.shiftKey ) {\n\t\t\t\treturn this.remove( $row );\n\t\t\t}\n\n\t\t\t// add class\n\t\t\t$row.addClass( '-hover' );\n\n\t\t\t// add tooltip\n\t\t\tvar tooltip = acf.newTooltip( {\n\t\t\t\tconfirmRemove: true,\n\t\t\t\ttarget: $el,\n\t\t\t\tcontext: this,\n\t\t\t\tconfirm: function () {\n\t\t\t\t\tthis.remove( $row );\n\t\t\t\t},\n\t\t\t\tcancel: function () {\n\t\t\t\t\t$row.removeClass( '-hover' );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tonClickRowOrder: function( e, $el ) {\n\t\t\tif ( ! this.get( 'pagination' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif ( $el.hasClass( 'disabled' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t$el.find( '.acf-row-number' ).hide();\n\t\t\t$el.find( '.acf-order-input' ).show().trigger( 'select' );\n\t\t},\n\n\t\tonBlurRowOrder: function( e, $el ) {\n\t\t\tthis.onChangeRowOrder( e, $el, false );\n\t\t},\n\n\t\tonChangeRowOrder: function( e, $el, update = true ) {\n\t\t\tif ( ! this.get( 'pagination' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst $row = $el.closest( '.acf-row' );\n\t\t\tconst $orderSpan = $row.find( '.acf-row-number' ).first();\n\t\t\tlet hrOrder = $el.val();\n\n\t\t\t$row.find( '.acf-order-input' ).first().hide();\n\n\t\t\tif ( ! acf.isNumeric( hrOrder ) || parseFloat( hrOrder ) < 0 ) {\n\t\t\t\t$orderSpan.show();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\thrOrder = Math.round( hrOrder );\n\n\t\t\tconst newOrder = hrOrder - 1;\n\n\t\t\t$el.val( hrOrder );\n\t\t\t$orderSpan.text( hrOrder ).show();\n\n\t\t\tif ( update ) {\n\t\t\t\tthis.updateRowStatus( $row, 'reordered', newOrder );\n\t\t\t}\n\t\t},\n\n\t\tonChangeTotalRows: function() {\n\t\t\tconst perPage = parseInt( this.get( 'per_page' ) ) || 20;\n\t\t\tconst totalRows = parseInt( this.get( 'total_rows' ) ) || 0;\n\t\t\tconst totalPages = Math.ceil( totalRows / perPage );\n\n\t\t\t// Update the total pages in pagination.\n\t\t\tthis.$( '.acf-total-pages:last' ).text( totalPages );\n\t\t\tthis.nextRowNum = totalRows;\n\n\t\t\t// If the current page no longer exists, load the last page.\n\t\t\tif ( this.page > totalPages ) {\n\t\t\t\tthis.page = totalPages;\n\t\t\t\tthis.ajaxLoadPage();\n\t\t\t}\n\t\t},\n\n\t\tremove: function ( $row ) {\n\t\t\tconst self = this;\n\n\t\t\tif ( this.get( 'pagination' ) ) {\n\t\t\t\tthis.decrementTotalRows();\n\n\t\t\t\t// If using pagination and the row had already been saved, just hide the row instead of deleting it.\n\t\t\t\tif ( $row.data( 'id' ).includes( 'row-' ) ){\n\t\t\t\t\tthis.updateRowStatus( $row, 'deleted' );\n\n\t\t\t\t\t$row.hide();\n\t\t\t\t\tself.$input().trigger( 'change' );\n\t\t\t\t\tself.render( false );\n\t\t\t\t\treturn;\n\t\t\t\t} else if ( $row.hasClass( 'acf-divider' ) ) {\n\t\t\t\t\t$row.next( '.acf-added' ).addClass( 'acf-divider' );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// If not using pagination, delete the actual row.\n\t\t\tacf.remove( {\n\t\t\t\ttarget: $row,\n\t\t\t\tendHeight: 0,\n\t\t\t\tcomplete: function () {\n\t\t\t\t\t// trigger change to allow attachment save\n\t\t\t\t\tself.$input().trigger( 'change' );\n\n\t\t\t\t\t// render\n\t\t\t\t\tself.render();\n\n\t\t\t\t\t// sync collapsed order\n\t\t\t\t\t//self.sync();\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tisCollapsed: function ( $row ) {\n\t\t\treturn $row.hasClass( '-collapsed' );\n\t\t},\n\n\t\tcollapse: function ( $row ) {\n\t\t\t$row.addClass( '-collapsed' );\n\t\t\tacf.doAction( 'hide', $row, 'collapse' );\n\t\t},\n\n\t\texpand: function ( $row ) {\n\t\t\t$row.removeClass( '-collapsed' );\n\t\t\tacf.doAction( 'show', $row, 'collapse' );\n\t\t},\n\n\t\tonClickCollapse: function ( e, $el ) {\n\t\t\t// vars\n\t\t\tvar $row = $el.closest( '.acf-row' );\n\t\t\tvar isCollpased = this.isCollapsed( $row );\n\n\t\t\t// shift\n\t\t\tif ( e.shiftKey ) {\n\t\t\t\t$row = this.$rows();\n\t\t\t}\n\n\t\t\t// toggle\n\t\t\tif ( isCollpased ) {\n\t\t\t\tthis.expand( $row );\n\t\t\t} else {\n\t\t\t\tthis.collapse( $row );\n\t\t\t}\n\t\t},\n\n\t\tonShow: function ( e, $el, context ) {\n\t\t\t// get sub fields\n\t\t\tvar fields = acf.getFields( {\n\t\t\t\tis: ':visible',\n\t\t\t\tparent: this.$el,\n\t\t\t} );\n\n\t\t\t// trigger action\n\t\t\t// - ignore context, no need to pass through 'conditional_logic'\n\t\t\t// - this is just for fields like google_map to render itself\n\t\t\tacf.doAction( 'show_fields', fields );\n\t\t},\n\n\t\tonUnload: function () {\n\t\t\t// vars\n\t\t\tvar indexes = [];\n\n\t\t\t// loop\n\t\t\tthis.$rows().each( function ( i ) {\n\t\t\t\tif ( $( this ).hasClass( '-collapsed' ) ) {\n\t\t\t\t\tindexes.push( i );\n\t\t\t\t}\n\t\t\t} );\n\n\t\t\t// allow null\n\t\t\tindexes = indexes.length ? indexes : null;\n\n\t\t\t// set\n\t\t\tpreference.save( this.get( 'key' ), indexes );\n\t\t},\n\n\t\tonHover: function () {\n\t\t\t// add sortable\n\t\t\tthis.addSortable( this );\n\n\t\t\t// remove event\n\t\t\tthis.off( 'mouseover' );\n\t\t},\n\n\t\tonChangeField: function( e, $el ) {\n\t\t\tconst $target = $( e.delegateTarget );\n\t\t\tlet $row = $el.closest( '.acf-row' );\n\n\t\t\tif ( $row.closest( '.acf-field-repeater' ).data( 'key' ) !== $target.data( 'key' ) ) {\n\t\t\t\t$row = $row.parent().closest( '.acf-row' );\n\t\t\t}\n\n\t\t\tthis.updateRowStatus( $row, 'changed' );\n\t\t},\n\n\t\tupdateRowStatus: function( $row, status, data = true ) {\n\t\t\tif ( ! this.get( 'pagination' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst parent_key = $row.parents( '.acf-field-repeater' ).data( 'key' );\n\n\t\t\tif ( this.parent() && parent_key !== this.get( 'key' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst row_id = $row.data( 'id' );\n\t\t\tconst input_name = this.$el.find( '.acf-repeater-hidden-input:first' ).attr( 'name' );\n\t\t\tconst status_name = `${input_name}[${row_id}][acf_${status}]`;\n\t\t\tconst status_input = `<input type=\"hidden\" class=\"acf-row-status\" name=\"${status_name}\" value=\"${data}\" />`;\n\n\t\t\tif ( ! $row.hasClass( 'acf-' + status ) ) {\n\t\t\t\t$row.addClass( 'acf-' + status );\n\t\t\t}\n\n\t\t\t// TODO: Update so that this doesn't get messed up with repeater subfields.\n\t\t\tconst $existing_status = $row.find( `input[name='${status_name}']` );\n\t\t\tif ( ! $existing_status.length ) {\n\t\t\t\t$row.find( 'td' ).first().append( status_input );\n\t\t\t} else {\n\t\t\t\t$existing_status.val( data );\n\t\t\t}\n\t\t},\n\n\t\tonClickFirstPage: function() {\n\t\t\tthis.validatePage( 1 );\n\t\t},\n\n\t\tonClickPrevPage: function() {\n\t\t\tthis.validatePage( this.page - 1 );\n\t\t},\n\n\t\tonClickNextPage: function( e ) {\n\t\t\tthis.validatePage( this.page + 1 );\n\t\t},\n\n\t\tonClickLastPage: function() {\n\t\t\tthis.validatePage( this.totalPages() );\n\t\t},\n\n\t\tonChangeCurrentPage: function() {\n\t\t\tthis.validatePage( this.$pageInput().val() );\n\t\t},\n\n\t\tmaybeDisablePagination: function() {\n\t\t\tthis.$actions().find( '.acf-nav' ).removeClass( 'disabled' );\n\n\t\t\tif ( this.page <= 1 ) {\n\t\t\t\tthis.$firstPageButton().addClass( 'disabled' );\n\t\t\t\tthis.$prevPageButton().addClass( 'disabled' );\n\t\t\t}\n\n\t\t\tif ( this.page >= this.totalPages() ) {\n\t\t\t\tthis.$nextPageButton().addClass( 'disabled' );\n\t\t\t\tthis.$lastPageButton().addClass( 'disabled' );\n\t\t\t}\n\t\t},\n\n\t\tvalidatePage: function( nextPage ) {\n\t\t\tconst self = this;\n\n\t\t\t// Validate the current page.\n\t\t\tacf.validateForm( {\n\t\t\t\tform: this.$control(),\n\t\t\t\tevent: '',\n\t\t\t\treset: true,\n\t\t\t\tsuccess: function( $form ) {\n\t\t\t\t\tself.page = nextPage;\n\n\t\t\t\t\t// Set up some sane defaults.\n\t\t\t\t\tif ( self.page <= 1 ) {\n\t\t\t\t\t\tself.page = 1;\n\t\t\t\t\t}\n\t\t\t\t\tif ( self.page >= self.totalPages() ) {\n\t\t\t\t\t\tself.page = self.totalPages();\n\t\t\t\t\t}\n\n\t\t\t\t\tself.ajaxLoadPage();\n\t\t\t\t},\n\t\t\t\tfailure: function( $form ) {\n\t\t\t\t\tself.$pageInput().val( self.page );\n\t\t\t\t\treturn false;\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tajaxLoadPage: function( clearChanged = false ) {\n\t\t\tconst ajaxData = acf.prepareForAjax( {\n\t\t\t\taction: 'acf/ajax/query_repeater',\n\t\t\t\tpaged: this.page,\n\t\t\t\tfield_key: this.get( 'key' ),\n\t\t\t\tfield_name: this.get( 'orig_name' ),\n\t\t\t\trows_per_page: parseInt( this.get( 'per_page' ) ),\n\t\t\t\trefresh: clearChanged\n\t\t\t} );\n\n\t\t\t$.ajax(\n\t\t\t\t{\n\t\t\t\t\turl: ajaxurl,\n\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\tdataType: 'json',\n\t\t\t\t\tdata: ajaxData,\n\t\t\t\t\tcontext: this,\n\t\t\t\t}\n\t\t\t).done(\n\t\t\t\tfunction( response ) {\n\t\t\t\t\tconst { rows } = response.data;\n\t\t\t\t\tconst $existingRows = this.$tbody().find( '> tr' );\n\n\t\t\t\t\t$existingRows.not( '.acf-clone' ).hide();\n\n\t\t\t\t\tif ( clearChanged ) {\n\t\t\t\t\t\t// Remove any existing rows since we are refreshing from the server.\n\t\t\t\t\t\t$existingRows.not( '.acf-clone' ).remove();\n\n\t\t\t\t\t\t// Trigger a change in total rows, so we can update pagination.\n\t\t\t\t\t\tthis.set( 'total_rows', response.data.total_rows, false );\n\t\t\t\t\t} else {\n\t\t\t\t\t\t$existingRows.not( '.acf-changed, .acf-deleted, .acf-reordered, .acf-added, .acf-inserted, .acf-clone' ).remove();\n\t\t\t\t\t}\n\n\t\t\t\t\tObject.keys( rows ).forEach( index => {\n\t\t\t\t\t\tlet $row         = false;\n\t\t\t\t\t\tlet $unsavedRow  = this.$tbody().find( '> *[data-id=row-' + index + ']' );\n\t\t\t\t\t\tlet $insertedRow = this.$tbody().find( '> *[data-inserted=' + index + ']' );\n\n\t\t\t\t\t\t// Unsaved new rows that are inserted into this specific position.\n\t\t\t\t\t\tif ( $insertedRow.length ) {\n\t\t\t\t\t\t\t$insertedRow.appendTo( this.$tbody() ).show();\n\t\t\t\t\t\t\tacf.doAction( 'remount', $insertedRow );\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Skip unsaved deleted rows; we don't want to show them again.\n\t\t\t\t\t\tif ( $unsavedRow.hasClass( 'acf-deleted' ) ) {\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Unsaved edited rows should be moved to correct position.\n\t\t\t\t\t\tif ( $unsavedRow.length ) {\n\t\t\t\t\t\t\tacf.doAction( 'unmount', $unsavedRow );\n\t\t\t\t\t\t\t$unsavedRow.appendTo( this.$tbody() ).show();\n\t\t\t\t\t\t\tacf.doAction( 'remount', $unsavedRow );\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Rows from the server (that haven't been changed or deleted) should be appended and shown.\n\t\t\t\t\t\t$row = $( rows[ index ] );\n\t\t\t\t\t\tthis.$tbody().append( $row ).show();\n\t\t\t\t\t\tacf.doAction( 'remount', $row );\n\n\t\t\t\t\t\t// Move clone field back to the right spot.\n\t\t\t\t\t\tthis.$clone().appendTo( this.$tbody() );\n\t\t\t\t\t} );\n\n\t\t\t\t\tconst $addedRows = this.$tbody().find( '.acf-added:hidden' );\n\n\t\t\t\t\t// If there are any new rows that are still hidden, append them to the bottom.\n\t\t\t\t\tif ( $addedRows.length ) {\n\t\t\t\t\t\tconst self = this;\n\n\t\t\t\t\t\t$addedRows.each( function() {\n\t\t\t\t\t\t\tconst $addedRow = $( this );\n\t\t\t\t\t\t\t$addedRow.insertBefore( self.$clone() ).show();\n\t\t\t\t\t\t\tacf.doAction( 'remount', $addedRow );\n\t\t\t\t\t\t} );\n\t\t\t\t\t}\n\n\t\t\t\t\t// Update the page input.\n\t\t\t\t\tthis.$pageInput().val( this.page );\n\t\t\t\t\tthis.maybeDisablePagination();\n\t\t\t\t}\n\t\t\t).fail(\n\t\t\t\tfunction( jqXHR, textStatus, errorThrown ) {\n\t\t\t\t\tconst error = acf.getXhrError( jqXHR );\n\t\t\t\t\tlet message = acf.__( 'Error loading page' );\n\n\t\t\t\t\tif ( '' !== error ) {\n\t\t\t\t\t\tmessage = `${message}: ${error}`;\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.showNotice( {\n\t\t\t\t\t\ttext: message,\n\t\t\t\t\t\ttype: 'warning',\n\t\t\t\t\t} );\n\t\t\t\t}\n\t\t\t);\n\t\t},\n\n\t} );\n\n\tacf.registerFieldType( Field );\n\n\t// register existing conditions\n\tacf.registerConditionForFieldType( 'hasValue', 'repeater' );\n\tacf.registerConditionForFieldType( 'hasNoValue', 'repeater' );\n\tacf.registerConditionForFieldType( 'lessThan', 'repeater' );\n\tacf.registerConditionForFieldType( 'greaterThan', 'repeater' );\n\n\t// state\n\tvar preference = new acf.Model( {\n\t\tname: 'this.collapsedRows',\n\n\t\tkey: function ( key, context ) {\n\t\t\t// vars\n\t\t\tvar count = this.get( key + context ) || 0;\n\n\t\t\t// update\n\t\t\tcount++;\n\t\t\tthis.set( key + context, count, true );\n\n\t\t\t// modify fieldKey\n\t\t\tif ( count > 1 ) {\n\t\t\t\tkey += '-' + count;\n\t\t\t}\n\n\t\t\t// return\n\t\t\treturn key;\n\t\t},\n\n\t\tload: function ( key ) {\n\t\t\t// vars\n\t\t\tvar key = this.key( key, 'load' );\n\t\t\tvar data = acf.getPreference( this.name );\n\n\t\t\t// return\n\t\t\tif ( data && data[ key ] ) {\n\t\t\t\treturn data[ key ];\n\t\t\t} else {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t},\n\n\t\tsave: function ( key, value ) {\n\t\t\t// vars\n\t\t\tvar key = this.key( key, 'save' );\n\t\t\tvar data = acf.getPreference( this.name ) || {};\n\n\t\t\t// delete\n\t\t\tif ( value === null ) {\n\t\t\t\tdelete data[ key ];\n\n\t\t\t\t// append\n\t\t\t} else {\n\t\t\t\tdata[ key ] = value;\n\t\t\t}\n\n\t\t\t// allow null\n\t\t\tif ( $.isEmptyObject( data ) ) {\n\t\t\t\tdata = null;\n\t\t\t}\n\n\t\t\t// save\n\t\t\tacf.setPreference( this.name, data );\n\t\t},\n\t} );\n} )( jQuery );\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import './_acf-field-repeater.js';\nimport './_acf-field-flexible-content.js';\nimport './_acf-field-gallery.js';\n"], "names": ["$", "Field", "acf", "extend", "type", "wait", "events", "showField", "unloadField", "mouseover", "$control", "$layoutsWrap", "$layouts", "$layout", "index", "$clonesWrap", "$clones", "$clone", "name", "$actions", "$button", "$popup", "getPopupHTML", "html", "$html", "countLayouts", "filter", "data", "length", "find", "each", "$a", "min", "max", "count", "addClass", "required", "title", "__", "identifier", "_n", "replace", "append", "outerHTML", "getValue", "allowRemove", "parseInt", "get", "val", "allowAdd", "isFull", "addSortable", "self", "sortable", "items", "handle", "forceHelperSize", "forcePlaceholderSize", "scroll", "stop", "event", "ui", "render", "update", "$input", "trigger", "addCollapsed", "indexes", "preference", "load", "i", "indexOf", "addUnscopedEvents", "on", "e", "onInvalidField", "initialize", "disable", "cid", "removeClass", "onShow", "$el", "context", "fields", "getFields", "is", "parent", "doAction", "validateAdd", "text", "showNotice", "onClickAdd", "hasClass", "closest", "popup", "Popup", "target", "targetConfirm", "confirm", "add", "layout", "before", "cancel", "args", "parseArgs", "duplicate", "proxy", "$el2", "enable", "onClickDuplicate", "duplicateLayout", "<PERSON><PERSON><PERSON>", "rename", "value", "search", "after", "focusAttention", "validate<PERSON><PERSON>ove", "onClickRemove", "shift<PERSON>ey", "removeLayout", "tooltip", "newTooltip", "confirmRemove", "endHeight", "remove", "complete", "onClickCollapse", "isLayoutClosed", "openLayout", "closeLayout", "renderLayout", "children", "prefix", "attr", "ajaxData", "action", "field_key", "serialize", "ajax", "url", "prepareForAjax", "dataType", "success", "onUnload", "push", "save", "onHover", "off", "registerFieldType", "models", "TooltipConfirm", "registerConditionForFieldType", "Model", "key", "set", "getPreference", "isEmptyObject", "setPreference", "j<PERSON><PERSON><PERSON>", "actions", "validation_begin", "validation_failure", "resize", "onValidationBegin", "$sideData", "onValidationFailure", "$collection", "$attachments", "$attachment", "id", "$active", "$main", "$side", "onUpdate", "start", "placeholder", "item", "removeAttr", "resizable", "handles", "minHeight", "update_user_setting", "size", "height", "$sort", "$add", "width", "columns", "Math", "round", "onResize", "openSidebar", "css", "animate", "right", "closeSidebar", "frame", "newMediaPopup", "mode", "field", "multiple", "library", "allowedTypes", "selected", "select", "attachment", "appendAttachment", "validateAttachment", "getInputName", "join", "$before", "eq", "renderAttachment", "alt", "filename", "attributes", "isget", "image", "src", "editAttachment", "button", "onClickEdit", "removeAttachment", "preventDefault", "stopPropagation", "selectAttachment", "step1", "step2", "has", "abort", "showLoading", "xhr", "cache", "step3", "onClickSelect", "onClickClose", "onChangeSort", "ids", "sort", "json", "isAjaxSuccess", "reverse", "map", "prepend", "$submit", "prev", "page", "nextRowNum", "change", "$table", "$tbody", "$rows", "not", "$row", "$firstPageButton", "$prevPageButton", "$nextPageButton", "$lastPageButton", "$pageInput", "totalPages", "numRows", "minRows", "maxRows", "isCollapsed", "expand", "$changed", "currentTarget", "onChangeField", "listenForSavedMetaBoxes", "update_order_numbers", "arguments", "undefined", "maybeDisablePagination", "<PERSON><PERSON><PERSON><PERSON>", "checkedMetaBoxes", "wp", "subscribe", "isSavingMetaBoxes", "ajaxLoadPage", "incrementTotalRows", "totalRows", "decrementTotalRows", "prevRowNum", "first", "newRowNum", "updateRowStatus", "hide", "message", "duplicateRow", "onClickRowOrder", "show", "onBlurRowOrder", "onChangeRowOrder", "$orderSpan", "hrOrder", "isNumeric", "parseFloat", "newOrder", "onChangeTotalRows", "perPage", "ceil", "includes", "next", "collapse", "isCollpased", "$target", "<PERSON><PERSON><PERSON><PERSON>", "status", "parent_key", "parents", "row_id", "input_name", "status_name", "status_input", "$existing_status", "onClickFirstPage", "validatePage", "onClickPrevPage", "onClickNextPage", "onClickLastPage", "onChangeCurrentPage", "nextPage", "validateForm", "form", "reset", "$form", "failure", "clearChanged", "paged", "field_name", "rows_per_page", "refresh", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "method", "done", "response", "rows", "$existingRows", "total_rows", "Object", "keys", "for<PERSON>ach", "$unsavedRow", "$insertedRow", "appendTo", "$addedRows", "$addedRow", "insertBefore", "fail", "jqXHR", "textStatus", "errorThrown", "error", "getXhrError"], "sourceRoot": ""}